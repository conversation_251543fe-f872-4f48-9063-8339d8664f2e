---
import { Image } from "astro:assets";

import CategoryTag from "@components/ai-test/CategoryTag.astro";
import TestResultList from "@components/ai-test/TestResultList.astro";
import DefaultIcon from "@assets/images/ai-test/tests-icons/arrow.jpg";

interface Props {
  testName: string;
  type?: "large" | "medium" | "small";
  video: string;
  categories: Array<string>;
  icon?: any;
  poster?: any;
  lang?: "ru" | "en";
}

const {
  testName,
  type = "large",
  video,
  categories,
  icon,
  poster,
  lang,
} = Astro.props;

let currentType = {
  large: "is--large",
  medium: "is--medium",
  small: "is--small",
};
---

<div class="test" class:list={[currentType[type], "swiper-slide"]}>
  <div class="test__video-wrap">
    <video
      class="test__video"
      style="width: 100%"
      controls
      playsinline
      poster={poster}
    >
      <source src={video} type="video/mp4" />
    </video>
  </div>
  <div class="test__info">
    <div class="test__name h3">{testName}</div>
    <div class="test__tags">
      {
        categories.map((category: any) => {
          return <CategoryTag lang={lang} type={category} />;
        })
      }
    </div>
    <div class="test__results">
      <TestResultList lang={lang} testName={testName} />
    </div>
  </div>
  {
    icon && (
      <Image
        class="test__icon"
        src={icon ?? DefaultIcon}
        alt={`Icon of ` + testName}
      />
    )
  }
</div>

<style>
  .test {
    background-color: var(--white);
    display: flex;
    border-radius: 16px;
    padding: 16px;
    gap: 32px;
    align-items: flex-start;
    flex-direction: column;
    overflow: hidden;
  }

  .test.is--large {
    grid-column: span 6;
    flex-direction: row;
  }

  @media screen and (max-width: 992px) {
    .test.swiper-slide {
      flex-direction: column;
      height: auto;
      width: 95%;
    }
  }

  .test.is--medium {
    grid-column: span 3;
  }

  .test.is--small {
    grid-column: span 2;
  }

  .test.is--medium .test__info,
  .test.is--small .test__info {
    flex: auto;
  }

  .test.is--small {
    grid-column: span 2;
  }

  .test.is--small .test__video-wrap {
    height: 480px;
  }

  .test__video-wrap {
    display: flex;
    position: relative;
    width: 100%;
    overflow: hidden;
    border-radius: 12px;
    -webkit-transform: translateZ(0);
  }

  video {
    object-fit: cover; /* so the video covers all the available space */
    object-position: center; /* not required */
  }

  video::-webkit-media-controls-timeline {
    display: none;
  }

  video::-webkit-media-controls-volume-slider {
    display: none;
  }

  video::-webkit-media-controls-mute-button {
    display: none;
  }

  @media all and (display-mode: fullscreen) {
    video {
      object-fit: contain;
    }

    video::-webkit-media-controls-timeline {
      display: block;
    }
  }

  .test__info {
    display: flex;
    flex-direction: column;
    flex: 0 0 32%;
    width: 100%;
  }

  .test__tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 16px;
  }

  .test__results {
    margin-top: 24px;
  }

  .test__icon {
    --icon-size: 64px;
    width: var(--icon-size);
    height: var(--icon-size);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100;
    border-radius: 8px;
  }
</style>

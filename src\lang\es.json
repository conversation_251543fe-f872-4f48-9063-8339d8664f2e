{"about": {"investors_cite": "La digitalización es inevitable para muchas industrias, y el deporte no es una excepción", "investors_cite_title": "Director de inversion<PERSON>", "investors_title": "Inversores", "media_title": "Medios de comunicación", "partners_title": "En colaboración con", "subtitle": "Creada en 2020 por un equipo transcultural de emprendedores e ingenieros informáticos", "subtitle_2": "", "text": "Combinamos tecnologías móviles, de IA y de visión por ordenador de última generación con experiencias de participación comunitaria para crear un centro digital para jugadores de fútbol, entrenadores, ojeadores y clubes de todo el mundo. Utilizando solo la cámara de un teléfono, nuestras aplicaciones (JuniStat y JuniCoach) pueden contar, seguir y trazar las habilidades futbolísticas al tiempo que proporcionan una revisión de vídeo y un profundo análisis estadístico.{br}{br}Basándonos en esta tecnología deportiva, estamos construyendo un TID (sistema de identificación de talentos) en el que los jóvenes jugadores tienen perfiles digitales y clasificaciones según sus logros. Este sistema recopila y proporciona datos estadísticos objetivos sobre los futbolistas a nivel mundial y aumenta significativamente las posibilidades de los jugadores de ser ojeados en línea, sin ninguna barrera, sino sólo gracias a su talento, persistencia y motivación.", "title": "JuniStat{copyright} es una empresa de inteligencia artificial móvil"}, "clubs": {"app_coach": "Aplicación para un entrenador", "connect_club": "Conectar el club", "create_club": "Crear cuenta de Club", "exercises": {"title": "14 pruebas digitales disponibles en la aplicación"}, "exercises_subtitle": "Las pruebas están integradas en la aplicación JuniCoach para que los entrenadores, los ojeadores y los jugadores puedan utilizarlas con mayor comodidad. Cada test muestra de forma fiable las métricas de un futbolista y los datos medios de su grupo de edad", "exercises_title": "Más de 15 ejercicios digitales", "features": {"card": {"description": "Perfil de marca con el logotipo del club", "title": "Tarjeta digital interactiva"}, "data": {"card_title": "Sobre el jugador", "description_text": "Futbolista apasionado y con ganas de triunfar. Alex es un delantero con un juego de pies rapidísimo, un excelente control del balón y un gran olfato de gol. Tiene un talento natural para leer el juego y tomar decisiones en fracciones de segundo que a menudo se traducen en oportunidades de gol para su equipo.", "height": "Altura, cm", "position": "Posición", "position_value": "CM, LCM", "title": "Datos adicionales", "weight": "Peso, kg"}, "results": {"title": "Resultados de las pruebas"}, "title": "Después de la prueba, cada jugador recibe", "video": {"title": "Los mejores videoclips del jugador"}}, "features_card_text1": "Cada jugador tendrá una tarjeta digital y un perfil de marca", "features_card_text2": "Todos los datos de los jugadores se protegen y almacenan de acuerdo con las normas GDPR, ISO y las normas de los países industriales. Cada jugador está vinculado a su representante adulto", "features_card_text3": "El club hace que los jugadores estén cerrados o abiertos a la vista según su criterio", "guide_title": "Manual paso a paso para la plataforma Juni", "hero": {"points": [{"they": "Láseres", "we": "Fácil de usar y de bajo coste"}, {"they": "Observaciones subjetivas", "we": "Estadísticas objetivas AI/ML"}, {"they": "Análisis tradicional", "we": "Más de 65 parámetros de rendimiento físico <br />y técnico"}], "reg_button": "<PERSON><PERSON><PERSON> cuenta", "reg_description": "Pruebe gratis 15 pruebas", "subtitle": "El sistema de pruebas inteligentes ayuda a academias y clubes a recopilar más de 65 puntos de datos para la identificación de talentos y la comprensión del desarrollo de los jóvenes.", "title": "Prueba, seguimiento y evaluación comparativa de las habilidades de los jugadores con la aplicación JuniCoach", "form": {"name_input": "Nombre completo", "email_input": "Correo electrónico", "phone_input": "Teléfono", "academy_input": "Nombre de la academia", "send_button": "Solicitar presentación", "legal_text": "Al hacer clic en el botón, aceptas la ", "legal_text_privacy": "política de privacidad", "form_error": "¡Lo sentimos! Parece que tu solicitud no se envió. Por favor, intenta enviarla más tarde. Si continúas teniendo problemas, aví<NAME_EMAIL>.", "form_success": "¡Gracias! Prepararemos la presentación para ti en breve y la enviaremos al correo electrónico especificado."}}, "how": {"equip": "Su club ya tiene el equipamiento necesario", "equip_item": {"cones": "Marcadores", "phone": "Smartphone", "tape": "<PERSON>inta métrica", "tripod": "<PERSON><PERSON>"}, "free_trial": "<PERSON><PERSON><PERSON> gratuita", "point1": {"title": "El entrenador graba los vídeos siguiendo las directrices de la aplicación."}, "point2": {"title": "El programa, basado en inteligencia artificial, los procesa inmediatamente y proporciona métricas precisas de los jugadores."}, "title": "Cómo funciona"}, "player_example_title": "Ejemplo de tarjetas personales de jugador", "player_open_profile": "Abrir tarjeta", "profile_protect_text": "Todos los datos de los jugadores se protegen y almacenan de acuerdo con las normas GDPR, ISO y las normas de los países industriales. Cada jugador está vinculado a su representante adulto", "quote": "Las pruebas sistemáticas aumentan significativamente el nivel de aprendizaje de los jóvenes jugadores y contribuyen al desarrollo de las habilidades futbolísticas básicas", "recommends": {"ian_text": "“The technology measures technical and physical player attributes that are scientifically tested to impact football (soccer) performance. Academy can now quickly test players using a smartphone and tripod, upload this data soon to a digital hub and drill deeper into an individual player’s performance levels. <br /> Data is now driving the entire talent ID and development process, and the Academy can access data quickly to make any required changes to a player’s individual training plan. <br /> When evaluating players for opportunities in Europe, the Academies strive to measure potential versus current performance. Hence, it is essential to track the rate at which young players develop over time and how consistently they improve.”", "show_less": "<PERSON>er menos", "show_more": "<PERSON>er más"}, "recommends_title": "Nos recomiendan", "rendered_subtitle": "Permite al club encontrar de forma rápida y eficaz a los jugadores con talento y llevar a cabo un scouting digital", "rendered_title": "Sistema de pruebas inteligente", "step_text1": "Registrar el club. Después de la aprobación, tendrá acceso al sistema.", "step_text2": "Introducir los datos de los entrenadores y jugadores en la cuenta personal del Club", "step_text3": "Póngase en contacto con nosotros para programar una fecha de prueba. Estaremos encantados de formar a sus formadores para que trabajen con el sistema", "steps": {"text1": "Supervisar y comparar los datos de los jugadores con los de sus homólogos de todo el mundo.", "text2": "<PERSON>lice los datos con sus herramientas o utilice nuestros conocimientos", "text3": "Exploración y selección de jugadores para las pruebas fuera de línea ", "title": "Inscríbase y explore las funciones de la plataforma en su panel web personal"}, "title": "JuniCoach{copyright} ayuda a probar rápidamente a los jugadores junto con el entrenamiento regular", "trial_testing": "<PERSON><PERSON><PERSON>"}, "faq": {"feedback": "¿Le ha ayudado esta respuesta?"}, "faq_list": [{"answer": "Por lo general, los resultados de las pruebas se devuelven dentro de 1 día. A veces, el procesamiento de una prueba puede demorar hasta 5 días. Siga las instrucciones cuando realice la prueba para reducir la posibilidad de rechazo", "title": "¿Cuánto tarda en procesarse la prueba? ¿Cuándo tendré los resultados?"}, {"answer": "Hay varias formas:<br /><ul><li>Envíe una invitación para unirse a un representante en la aplicación del reproductor</li><li>Registrese como representante - <a href=\"https ://aplicación. junistat.com/sign-up?role=mentor\">app.junistat.com/sign-up?role=mentor</a> y enviar una invitación al jugador</li><li>Si el jugador está registrado a través de la academia, solicite a la academia de empleados que lo agregue como representante</li></ul>", "title": "¿Cómo conectar un representante a un jugador?"}, {"answer": "Solo puede pagar la suscripción en la cuenta del representante.<br /><ol><li>Inicie sesión en la cuenta del representante <a href=\"https://app.junistat.com/login? role=mentor\">app.junistat.com/login?role=mentor</a></li><li>Seleccione el jugador al que desea suscribirse</li><li>Haga clic en \"Suscribirse\"</li></ol> ", "title": "¿Cómo pagar una suscripción?"}, {"answer": "<PERSON><PERSON><PERSON> seguir los resultados del jugador en su cuenta de representante - <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role= mentor</a> o en la calificación general en el sitio si el perfil del jugador está abierto: <a href=\"https://junistat.com/\">junistat.com</a>", "title": "¿Cómo realizar un seguimiento de los resultados de su hijo?"}, {"answer": "<ul><li>Los resultados se vuelven menos relevantes con el tiempo. Realice pruebas regularmente para mantener su máximo calificación. </li> <li>Los puntajes promedio de las pruebas están mejorando. Están apareciendo nuevos jugadores con un mejor rendimiento que usted, y su calificación se reduce en consecuencia.</li><li>Los puntajes de los jugadores han empeorado.</li></ul>", "title": "¿Por qué está bajando la calificación?"}, {"answer": "<ol><li>Iniciar sesión en la cuenta del representante <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role=mentor </a></li><li>Seleccione el reproductor para el que desea agregar un video o una descripción.</li><li>Seleccione la pestaña \"Acerca de\" en el menú</li><li> Agrega una descripción o sube un video</li><li>Haz clic en \"Guardar\"</li></ol>", "title": "¿Cómo agrego un video y una descripción del reproductor?"}, {"answer": "<ol><li>Iniciar sesi<PERSON> en la cuenta del representante <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role=mentor </a></li><li><PERSON>ga clic en el botón \"Combinar perfiles\"</li><li>Seleccione los perfiles de los jugadores de la academia y la aplicación JuniStat</li></ol>", "title": "¿Cómo fusionar perfiles de jugadores? 2 jugadores idénticos en la cuenta de representante, ¿cómo dejar uno?"}, {"answer": "La suscripción da acceso a todas las pruebas. Posibilidad de que el jugador esté disponible para que lo vean academias y clubes", "title": "¿Para qué es la suscripción?"}, {"answer": "Puedes registrarte en la aplicación JuniStat, realizar pruebas y tal vez uno de los exploradores, academias o clubes se fije en ti y te invite a su lugar", "title": "¿Cómo puedo ver clubes y academias? ¿Cómo me uno a una academia?"}, {"answer": "<ol><li>Iniciar sesión en la cuenta del representante <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role=mentor </a></li><li>Seleccionar jugador</li><li>En la página del jugador seleccionado, ve a la sección \"Configuración\"</li><li>Al final de la página , haz clic en el botón \"Desconectar jogador\"</li></ol>", "title": "¿Cómo desvincular a un representante de un jugador?"}, {"answer": "Envíenos un correo electrónico <a href=\"mailto:<EMAIL>\"><EMAIL></a>. Ingrese el nombre, número de teléfono o correo electrónico del jugador.", "title": "¿Cómo eliminar una cuenta?"}, {"answer": "El jugador no necesita tener un representante para hacer esto. Pídele al representante actual que desvincule al jugador de su perfil.", "sub_answers": [{"answer": "<h4>Enviando un correo electrónico al representante</h4><ol><li>Inicie sesión en la cuenta de la academia <a href=\"https://app.junistat.com/login?role=academy\">app.junistat.com/login?role=academy</a></li><li>En la lista de jugadores, haga clic en \"Invitar a un representante\"</li><li>Ingrese el correo electrónico del representante y haga clic en \"Enviar solicitud\"</li></ol><br /><h4>Compartir perfil</h4><ol><li>Iniciar sesión en Academy <a href=\"https://app.junistat. com/login?role=academy\">app.junistat.com/login?role=academy</a> </li><li>En la lista de jugadores o en la página del jugador, haga clic en \"Compartir\"</li><li>Envíe el enlace al representante de cualquier manera.</li> <li>El representante debe registrarse usando este enlace.</li></ol>", "title": "En la oficina de la academia"}, {"answer": "<ol><li><PERSON><PERSON><PERSON> la lista de jugadores</li> <li>En frente al reproductor, haga clic en el icono \"Invitar representante\"</li> <li>Especifique la dirección de correo electrónico del representante y haga clic en \"Enviar\"</li></ol>", "title": "En la aplicación JuniCoach"}], "title": "¿Cómo enviar una invitación a un representante?", "type": "academy"}, {"answer": "Contacta al representante actual para desvincular al jugador de su perfil. Cuando el jugador esté libre del representante, puedes invitar a uno nuevo.", "title": "¿Cómo reemplazar al representante del jugador?", "type": "academy"}], "meta": {"description": "Aplicaciones inteligentes para jóvenes futbolistas, entrenadores y clubes. Clasificación internacional, pruebas, identificación de talentos y scouting"}, "navigation": {"about": "Sobre nosotros", "clubs": "Clubes, Entrenadores, Scouts", "faq": "FAQ", "parents": "Padres y representantes", "press_kit": "Do<PERSON>r de prensa", "privacy": "Política de privacidad", "rating": "Rating", "rating_system": "Sistema de clasificación", "scouting": "Digital scouting", "sponsors": "Patrocinadores", "team": "Equipo"}, "parents": {"about_cards": "Carnet personal, perfil digital y rating en la base internacional de futbolistas jóvenes", "about_skills_subtitle": "Oportunidad de competir y compararse con compañeros de todo el mundo", "about_skills_title": "Una evaluación objetiva de las habilidades y recomendaciones del fútbol para el desarrollo", "app_for_player": "Aplicación para jugador", "chart_text": "Los datos sobre el rendimiento técnico y físico de un joven futbolista, su biografía y los mejores momentos de sus partidos se recopilan en un perfil digital. Dependiendo de la calificación y los logros, el futbolista puede recibir una invitación de los clubes", "create_parent_profile": "Crear un perfil representativo", "title": "JuniStat{copyright} identifica jóvenes talentos de fútbol"}, "pricing": {"from_3000": "from 3,000 players", "from_3000_price_month": "{currency}4/month", "from_3000_price_year": "{currency}40/year", "license": {"point1": "Unlimited access to tests in JuniStat and JuniCoach mobile apps", "point2": "Digital cards and player profiles with an objective evaluation skills", "point3": "Dynamics of development, players' benchmarking, and record of progress (digital track", "point4": "Player ratings in the international database of young football players", "point5": "Development tips, insights, and recommendations for players and coaches", "point6": "A personal web account of the Academy with statistics, analytics, videos, selection block, and reports", "title": "License includes"}, "price_player": "Price per player", "price_player_description": "1 player = 1 license", "title": "Plans for Business", "up_to_1000": "up to 1 000 players", "up_to_1000_price_month": "{currency}5/month", "up_to_1000_price_year": "{currency}50/year", "up_to_3000": "from 1,000 to 3,000 players", "up_to_3000_price_month": "{currency}4.5/month", "up_to_3000_price_year": "{currency}45/year"}, "rating": {"banner": {"subtitle": "Comienza las pruebas descargando la aplicación", "title": "Obtenga su calificación"}, "card_clubs": "Solución para los clubes", "card_parents": "Padres y representantes", "example": {"point1": "Ficha personal con evaluación imparcial de las competencias", "point2": "Perfil digital con datos de rendimiento objetivos y destacados", "point3": "Calificación en la base de talentos internacionales", "point4": "Oportunidad de ser explorado en online", "title": "Cada jugador tiene"}, "filters": "<PERSON><PERSON><PERSON>", "grid": {"error": "Error al cargar los jugadores. Actualice la página o inténtelo más tarde", "next": "Siguient<PERSON>", "prev": "Anterior", "empty": "Jugadores no encontrados u ocultos"}, "popup": {"button_login": "In<PERSON><PERSON>", "button_signUp": "<PERSON><PERSON><PERSON> cuenta", "description": "Para obtener acceso completo a la base de datos de jugadores y a la búsqueda avanzada", "title": "<PERSON><PERSON> su cuenta"}, "search_placeholder": "Nombre del jugador", "select_club": "Elige un club", "titleAllPlayers": "<PERSON><PERSON><PERSON>", "title_players_month": "Jugadores del Mes", "subtitle_players_month": "Completa las pruebas y entra en el TOP de futbolistas del mes. Se tienen en cuenta tanto los resultados como la actividad", "scout": {"title": "¿Eres un ojeador, agente o representante de una academia de fútbol?", "text": "Recibe informes analíticos mensuales y conocimientos sobre los mejores jugadores que han pasado nuestras pruebas. Descubre futuras estrellas del fútbol con JuniStat", "button": "Suscribirse", "terms": "Al enviar este formulario, aceptas la <a href='https://junistat.com/privacy/' target='_blank'>Política de Privacidad</a>", "form-success": "Gracias por su suscripción. Recibirá nuestro próximo correo con los mejores jugadores del mes", "form-error": "¡Lo sentimos! Parece que su envío no se realizó. Por favor, intenta enviarlo nuevamente más tarde. Si continúa teniendo problemas, aví<NAME_EMAIL>"}}, "sign_up": "<PERSON><PERSON><PERSON> cuenta", "Top 5 Startups 2025": "Top 5 Startups 2025", "sponsors": {"contact": "No dude en contactar — <PERSON><PERSON><PERSON>", "text": "Hay más de 100.000 jóvenes futbolistas en todo el mundo que utilizan el servicio. Cada día se registran nuevos talentos, pero no todos son solventes. Los patrocinadores pueden ayudar a los jugadores a pagar la suscripción. Existen paquetes a partir de 100 abonos.", "title": "¡Ayude a los jóvenes jugadores a tener una oportunidad!"}, "team": {"ambassador_title": "Embajadores", "how_become_ambassador_text": "Si desea representar a JuniStat en su país, contáctenos —", "how_become_ambassador_title": "¿Cómo ser nuestro embajador?", "team_title": "Gestión y operación"}}
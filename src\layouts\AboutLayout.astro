---
//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

//#endregion [i18n]

//#region [Styles]
import "@styles/pages/about.scss";
//#endregion [Styles]

//#region [Built-in components]
import { Image } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
//#endregion [Components]

//#region [Icons]
//#endregion [Icons]

//#region [Images]
import AngelsDeckImg from "@assets/images/about/angel-desk.svg";
import IlyaPartinImg from "@assets/images/about/ilya_partin.jpg";
import BrayneImg from "@assets/images/about/brayne.svg";
import NvidiaLogoImg from "@assets/images/about/logos/nvidia.png";
import AwsLogoImg from "@assets/images/about/logos/aws-sturtup.png";
import StartupChileLogoImg from "@assets/images/about/logos/startup-chile.png";
import OracleLogoImg from "@assets/images/about/logos/oracle.png";
import ContinentalLogoImg from "@assets/images/about/logos/continental.png";
import HpcLogoImg from "@assets/images/about/logos/hpc-park.svg";

//#endregion [Images]
---

<Layout>
  <!-- Header -->
  <section id="hero">
    <div class="container">
      <div class="hero">
        <h1
          class="hero__title h2"
          set:html={t("about.title", {
            copyright: `<sup style="font-size: 2.4rem">®</sup>`,
          })}
        />
        {
          () => {
            if (lang !== "ru") {
              return (
                <>
                  <h2 class="hero__title-subtitle h3">{t("about.subtitle")}</h2>
                  <h2 class="hero__title-subtitle h3">
                    {t("about.subtitle_2")}
                  </h2>
                </>
              );
            }
          }
        }

        <p
          class="hero__copy"
          set:html={t("about.text", {
            br: `<br />`,
          })}
        />
      </div>
    </div>
  </section>
  <!-- Investors -->
  <section id="investors" class="section-space">
    <div class="container">
      <div class="investors">
        <h2 class="investors__title">{t("about.investors_title")}</h2>
        <div class="investors__content">
          <div class="investors__cite">
            <q class="investors__cite-text">
              {t("about.investors_cite")}
            </q>
            <div class="investors__cite-footer">
              <Image
                class="investors__cite-avatar"
                src={IlyaPartinImg}
                alt="Ilya Partin"
              />
              <p class="investors__cite-info p2">
                Ilya Partin,
                <span style="display: block"
                  >{t("about.investors_cite_title")}</span
                >
              </p>
            </div>
          </div>
          <div class="investors__logos">
            <a
              class="investors__investor-card"
              href="https://www.adgv.vc/"
              target="_blank"
              rel="nofollow"
            >
              <Image
                class="investors__logo"
                src={AngelsDeckImg}
                alt="AngelsDeck"
              />
              <p>AngelsDeck</p>
            </a>
            <a
              class="investors__investor-card"
              href="https://brayne.vc/"
              target="_blank"
              rel="nofollow"
            >
              <Image class="investors__logo" src={BrayneImg} alt="Brayne" />
              <p>Brayne</p>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- Media -->
  <section id="media" class="section-space">
    <div class="container">
      <div class="media">
        <h2 class="media__title">{t("about.media_title")}</h2>
        <div class="media__content">
          {
            lang === "ru" && (
              <a
                class="media__card"
                href="https://qjl.kz/ru/news/digitalisation-in-youth-football"
                target="_blank"
                rel="nofollow"
              >
                <p class="media__card-text">
                  Freedom QJ League провела цифровое тестирование игроков
                </p>
                <p class="media__card-link p3">qjl.kz</p>
              </a>
            )
          }
          {
            lang === "ru" && (
              <a
                class="media__card"
                href="https://dzen.ru/a/ZcFY7XY11GsTjwDt"
                target="_blank"
                rel="nofollow"
              >
                <p class="media__card-text">
                  Программа цифровизации учеников «Зенит-Чемпионика»
                </p>
                <p class="media__card-link p3">dzen.ru</p>
              </a>
            )
          }

          <a
            class="media__card"
            href="https://www.sportsbusinessjournal.com/Daily/Issues/2022/08/17/Technology/digital-scouting-data-metrics"
            target="_blank"
            rel="nofollow"
          >
            <p class="media__card-text">
              Soccer Is Using New Devices, Sensors to Evaluate Players'
              Technical Skills and Tactical Style
            </p>
            <p class="media__card-link p3">sporttechie.com</p>
          </a>
          <a
            class="media__card"
            href="https://r.email.sport-gsic.com/mk/mr/EGfeN2WdCnBbg0ompcyxlXRO5QrMzyGRQXqCtyiNLRVodElky79K0HDyOz_sF_H8qh0koto5smc4OQ938N9RESoSldMpG82G5Qku0Wzt-TqR4rQcKnt6vo1bphVTGIa7Htwu1A"
            target="_blank"
            rel="nofollow"
          >
            <p class="media__card-text">
              JuniStat starts digital scouting programs in Africa
            </p>
            <p class="media__card-link p3">sport-gsic.com</p>
          </a>
          <a
            class="media__card"
            href="https://startupchile.org/blog/ignite-3-new-generation/"
            target="_blank"
            rel="nofollow"
          >
            <p class="media__card-text">
              The selected startups for the new generation of ignite are
            </p>
            <p class="media__card-link p3">startupchile.org</p>
          </a>
        </div>
      </div>
    </div>
  </section>
  <!-- Partners -->
  <section id="partners" class="section-space">
    <div class="container">
      <div class="partners">
        <h2 class="partners__title">{t("about.partners_title")}</h2>
        <div class="partners__content">
          <div class="partners__logos">
            <Image src={NvidiaLogoImg} class="partners__logo" alt="Nvidia" />
            <Image src={AwsLogoImg} class="partners__logo" alt="AWS Sturtup" />
            <Image
              src={StartupChileLogoImg}
              class="partners__logo"
              alt="Startup Chile"
            />
            <Image src={OracleLogoImg} class="partners__logo" alt="Oracle" />
            <Image
              src={ContinentalLogoImg}
              class="partners__logo"
              alt="Continental Startups"
            />
            <a
              href="https://hpc-park.ru/partnership_junistat"
              target="_blank"
              rel="nofollow"
            >
              <Image src={HpcLogoImg} class="partners__logo" alt="HPC Park" />
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout>

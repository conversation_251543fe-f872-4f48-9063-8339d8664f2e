---
//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
//#endregion [i18n]

import Layout from "@layouts/Layout.astro";

import AppButton from "@components/AppButton.astro";
---

<Layout noindex>
  <div class="container">
    <div class="price__card" style="grid-area: form;">
      <h1 style="font-size: 32px">{t("delete_account.title")}</h1>
      <p>
        {t("delete_account.description")}
      </p>
      <div class="card">
        <form class="price__form">
          <label for="email">
            <span class="price__form-label">Email</span>
            <input
              class="price__form-input"
              type="email"
              name="email"
              id="email"
            />
          </label>
          <AppButton as="button" type="submit" id="form_button">
            <span style="font-size: 16px">{t("delete_account.button")}</span>
          </AppButton>
        </form>
        <div class="form__error">
          {t("delete_account.error")}
        </div>
        <div class="form__success">
          {t("delete_account.success")}
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  //#region [Form]
  const form = document.querySelector(".price__form") as HTMLFormElement;

  form.addEventListener("submit", async (e) => {
    e.preventDefault();
    const formData = new FormData(form);

    const userEmail = formData.get("email");

    try {
      const sendData = await fetch(
        "https://delete-account-bot.pages.dev" + "/api/submit",
        {
          method: "POST",
          body: JSON.stringify({
            email: userEmail,
          }),
        },
      );

      const successMessage = document.querySelector(
        ".form__success",
      ) as HTMLElement;

      successMessage.style.display = "block";
      form.style.display = "none";
    } catch (error) {
      const errorMessage = document.querySelector(
        ".form__error",
      ) as HTMLElement;
      errorMessage.style.display = "block";
    }
  });
  //#endregion [Form]
</script>

<style>
  .container {
    max-width: 640px;
    margin-right: auto;
    margin-left: auto;
    margin-top: 48px;
  }

  h1 {
    margin-bottom: 16px;
  }

  p {
    margin-bottom: 16px;
  }

  .card {
    background-color: var(--white);
    padding: 16px;
    border-radius: 8px;
  }

  /*#region [Form]*/
  .price__form > label {
    display: block;
  }

  .price__form > label + label {
    margin-top: 24px;
  }

  .price__form-label {
    display: block;
  }

  .price__form-input {
    padding: 8px 16px;
    width: 100%;
    background-color: #f4f5f9;
    border: none;
    border-radius: 6px;
  }

  .price__form-agrement {
    margin-top: 24px;
    margin-bottom: 24px;
  }

  .form__success {
    display: none;
    padding: 16px;
    background-color: rgb(15, 143, 49);
    border-radius: 8px;
    color: white;
    margin-top: 24px;
  }

  .form__error {
    display: none;
    padding: 16px;
    background-color: rgb(143, 15, 49);
    border-radius: 8px;
    color: white;
    margin-top: 24px;
  }

  #form_button {
    margin-top: 16px;
  }

  /*#endregion [Form]*/
</style>

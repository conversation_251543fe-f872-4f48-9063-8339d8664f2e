---
import { getCollection } from "astro:content";
import Layout from "@layouts/Layout.astro";
import { Image } from "astro:assets";

//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils.ts";

const autoLang = getLangFromUrl(Astro.url);
const t = useTranslations(autoLang);
//#endregion [i18n]

const { lang = "en" } = Astro.props;
const collectionPath: string = lang === "en" ? "hc" : `hc-${lang}`;

// @ts-ignore
const posts = (await getCollection(collectionPath)).sort(
  (b, a) => a.data.date.valueOf() - b.data.date.valueOf(),
);
---

<Layout>
  <div class="container articles">
    <h1>{t("help_center.title")}</h1>
    <ul class="posts-grid">
      {
        posts.map((post) => {
          return (
            <li>
              <a href={post.slug}>
                <Image
                  class={"posts__cover"}
                  src={post.data.cover}
                  alt={post.data.title}
                />
                {post.data.title}
              </a>
            </li>
          );
        })
      }
    </ul>
  </div>
</Layout>

<style>
  .articles {
    margin-top: 48px;
  }

  h1 {
    font-size: 40px;
    margin-bottom: 32px;
  }

  ul {
    list-style-type: none;
    padding: 0;
  }

  a:hover {
    color: var(--blue);
  }

  .posts-grid {
    display: grid;
    gap: 32px 24px;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .posts__cover {
    width: 100%;
    aspect-ratio: 2/1;
    min-height: 152px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
    object-position: top;
    transition: transform 0.3s var(--smooth-ease);
  }

  @media screen and (any-hover: hover) {
    li:hover .posts__cover {
      transform: scale(1.02);
    }
  }

  @media screen and (max-width: 688px) {
    .posts__cover {
      height: auto;
      aspect-ratio: 2/1;
    }
  }
</style>

---
//#region [Built-in components]
import { Image, Picture, getImage } from "astro:assets";
//#endregion [Built-in components]

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

//#region [Components]
import Layout from "@layouts/Layout.astro";
import RegisterButton from "@components/junicoach/RegisterButton.astro";
import TestCard from "@components/ai-test/TestCard.astro";

//#endregion [Components]

//#region [Styles]
import "@styles/pages/junicoach.css";
//#endregion [Styles]

//#region [Images]
import HeroPhone from "@assets/images/junicoach/junicoach-record.png";
import RfsLetter from "@assets/images/clubs/recommends/rfs-ru.jpg";

import IconRub from "@assets/images/junicoach/icon-rub.png";
import IconStar from "@assets/images/junicoach/icon-star.png";
import IconRocket from "@assets/images/junicoach/icon-rocket.png";

import PlayersCards from "@assets/images/junicoach/players-cards.png";

// Tests icons
import Run15upIcon from "@assets/images/ai-test/tests-icons/15m-run-up.jpg";
import PushUpsIcon from "@assets/images/ai-test/tests-icons/push-ups.jpg";
import ArrowBallIcon from "@assets/images/ai-test/tests-icons/arrow-ball.jpg";
import ArrowIcon from "@assets/images/ai-test/tests-icons/arrow.jpg";
import Dribbling15Icon from "@assets/images/ai-test/tests-icons/dribbling15-ball.jpg";
import jumpIcon from "@assets/images/ai-test/tests-icons/jump-place.jpg";
import LadderFBIcon from "@assets/images/ai-test/tests-icons/ladder-front-back.jpg";
import LadderLRIcon from "@assets/images/ai-test/tests-icons/ladder-left-right.jpg";
import KickIcon from "@assets/images/ai-test/tests-icons/monster-kick-right.jpg";
import Run15Icon from "@assets/images/ai-test/tests-icons/run-15.jpg";
import SerpentIcon from "@assets/images/ai-test/tests-icons/serpient-ball.jpg";

// Posters
import Run15UpPoster from "@assets/images/ai-test/video-posters/poster_15mRunUp-min.jpg";
import Sprint15Poster from "@assets/images/ai-test/video-posters/poster_15mSprint-min.jpg";
import ArrowPoster from "@assets/images/ai-test/video-posters/poster_arrow-min.jpg";
import ArrowBallPoster from "@assets/images/ai-test/video-posters/poster_arrowBall-min.jpg";
import DribblingPoster from "@assets/images/ai-test/video-posters/poster_dribbling-min.jpg";
import JumpPoster from "@assets/images/ai-test/video-posters/poster_jump-min.jpg";
import KickPoster from "@assets/images/ai-test/video-posters/poster_kick-min.jpg";
import LadderBFPoster from "@assets/images/ai-test/video-posters/poster_ladderBF-min.jpg";
import LadderLRPoster from "@assets/images/ai-test/video-posters/poster_ladderLR-min.jpg";
import PushUpsPoster from "@assets/images/ai-test/video-posters/poster_pushups-min.jpg";
import SerpentPoster from "@assets/images/ai-test/video-posters/poster_serpent-min.jpg";

const posters = {
  Run15UpPoster: Run15UpPoster,
  Sprint15Poster: Sprint15Poster,
  ArrowPoster: ArrowPoster,
  ArrowBallPoster: ArrowBallPoster,
  DribblingPoster: DribblingPoster,
  JumpPoster: JumpPoster,
  KickPoster: KickPoster,
  LadderBFPoster: LadderBFPoster,
  LadderLRPoster: LadderLRPoster,
  PushUpsPoster: PushUpsPoster,
  SerpentPoster: SerpentPoster,
};

const optimizedPosters: any = {};
for (const [key, value] of Object.entries(posters)) {
  const optimizedPoster = await getImage({ src: value, format: "webp" });
  optimizedPosters[key] = optimizedPoster;
}

//#endregion [Images]

//#region [Videos]
import m15SprintMP4 from "@assets/videos/ai-tests/15m-sprint.mp4";
import m15SprintFromRunMP4 from "@assets/videos/ai-tests/15m-from-run-up.mp4";
import arrowWithBallMP4 from "@assets/videos/ai-tests/arrow-with-ball.mp4";
import arrowMP4 from "@assets/videos/ai-tests/arrow.mp4";
import dribblingMP4 from "@assets/videos/ai-tests/dribbling.mp4";
import highJumpMP4 from "@assets/videos/ai-tests/jump.mp4";
import ladderBFMP4 from "@assets/videos/ai-tests/ladder-b-f.mp4";
import ladderLRMP4 from "@assets/videos/ai-tests/ladder-l-r.mp4";
import powerKickMP4 from "@assets/videos/ai-tests/power-kick.mp4";
import pushUpsMP4 from "@assets/videos/ai-tests/push-ups.mp4";
import serpentMP4 from "@assets/videos/ai-tests/serpent.mp4";
//#endregion [Videos]

interface Props {
  prices?: { day: number; month: number; year: number };
}
const { prices } = Astro.props;
---

<Layout
  contentOnly
  title="JuniCoach — Система футбольных тестирований со смартфона"
  description="Идеальный инструмент для дополнительного заработка школы, мотивации и прогресса юных футболистов"
  ogImage="https://junistat.com/og-junicoach.jpg"
>
  <header>
    <div class="container">
      <div class="header">
        <div class="header__logo-wrap">
          <svg
            class="header__logo"
            xmlns="http://www.w3.org/2000/svg"
            width="227"
            height="42"
            fill="none"
            viewBox="0 0 227 42"
            ><path
              fill="#FF651D"
              d="M92.736 4.18A5.15 5.15 0 0 1 97.795 0h123.347a5.145 5.145 0 0 1 3.984 1.888 5.148 5.148 0 0 1 1.063 4.278l-6.4 31.704a5.147 5.147 0 0 1-5.047 4.13H91.722a5.15 5.15 0 0 1-5.06-6.118L92.737 4.18ZM8.311 36.014c-1.943 0-3.62-.327-5.03-.984C1.872 34.373.778 33.426 0 32.187l3.901-3.719c1.118 1.8 2.613 2.698 4.484 2.698 2.284 0 3.693-1.325 4.228-3.974L15.056 14.8h-8.93l.947-4.739h14.8L18.481 26.9c-.631 3.258-1.775 5.59-3.426 7-1.653 1.41-3.901 2.114-6.745 2.114Zm37.547-20.049-3.901 19.612h-5.394l.4-2.115a8.67 8.67 0 0 1-2.916 1.823c-1.112.393-2.284.59-3.463.585-2.138 0-3.851-.585-5.14-1.751-1.264-1.166-1.895-2.807-1.895-4.921 0-.802.084-1.591.255-2.37l2.151-10.863h5.687l-2.078 10.498a6.826 6.826 0 0 0-.146 1.422c0 2.042 1.094 3.062 3.28 3.062 1.434 0 2.613-.413 3.536-1.238.948-.852 1.591-2.116 1.932-3.791l2.006-9.953h5.686Zm16.678-.292c2.186 0 3.923.585 5.211 1.751 1.289 1.142 1.932 2.781 1.932 4.92a11 11 0 0 1-.255 2.37l-2.186 10.863H61.55l2.116-10.499c.1-.442.148-.895.144-1.348 0-1.02-.279-1.798-.838-2.333-.559-.535-1.396-.801-2.514-.801-1.459 0-2.673.425-3.647 1.274-.947.826-1.59 2.078-1.931 3.755l-1.968 9.952h-5.687l3.9-19.612h5.396l-.437 2.116c1.749-1.605 3.899-2.408 6.452-2.408Zm13.793.292h5.65l-3.901 19.612h-5.65l3.9-19.612Zm3.79-2.734c-.971 0-1.773-.28-2.406-.838a2.825 2.825 0 0 1-.91-2.114c0-.972.34-1.773 1.02-2.406.706-.656 1.615-.984 2.734-.984.997 0 1.799.28 2.406.84.631.532.948 1.201.948 2.003 0 1.046-.353 1.896-1.058 2.553-.704.63-1.615.946-2.733.946Z"
            ></path><path
              fill="#fff"
              d="M109.808 35.326c-2.55 0-4.786-.46-6.706-1.384-1.896-.924-3.354-2.224-4.374-3.901-1.022-1.676-1.533-3.609-1.533-5.797 0-2.89.657-5.49 1.97-7.799 1.336-2.334 3.208-4.167 5.612-5.504 2.407-1.336 5.166-2.005 8.275-2.005 2.285 0 4.302.388 6.051 1.166 1.75.777 3.074 1.895 3.974 3.354l-4.264 3.499c-1.362-1.992-3.414-2.988-6.161-2.988-1.872 0-3.525.437-4.958 1.31a9.127 9.127 0 0 0-3.354 3.573c-.777 1.507-1.166 3.184-1.166 5.031 0 1.968.619 3.537 1.86 4.703 1.264 1.142 3.025 1.713 5.285 1.713 2.697 0 4.957-.984 6.781-2.953l3.498 3.572c-2.527 2.941-6.123 4.41-10.79 4.41Zm22.523-.146c-1.97 0-3.707-.363-5.214-1.092-1.483-.73-2.637-1.75-3.462-3.062-.802-1.336-1.204-2.88-1.204-4.63 0-2.162.511-4.108 1.532-5.831a11.008 11.008 0 0 1 4.228-4.083c1.798-.998 3.827-1.495 6.087-1.495 1.994 0 3.731.364 5.214 1.094 1.481.73 2.623 1.761 3.426 3.098.826 1.312 1.239 2.843 1.239 4.592 0 2.164-.511 4.108-1.531 5.833-.996 1.725-2.407 3.086-4.228 4.083-1.799.996-3.827 1.493-6.087 1.493Zm.364-4.7c1.093 0 2.078-.269 2.952-.802a5.682 5.682 0 0 0 2.042-2.298c.485-.972.729-2.09.729-3.354 0-1.335-.389-2.393-1.166-3.17-.779-.778-1.872-1.167-3.282-1.167-1.118 0-2.114.28-2.988.838a5.42 5.42 0 0 0-2.042 2.26c-.486.972-.729 2.09-.729 3.354 0 1.337.389 2.393 1.166 3.172.802.777 1.908 1.166 3.318 1.166Zm37.045-15.203-3.9 19.612h-5.395l.364-1.931c-1.627 1.483-3.632 2.224-6.015 2.224-1.53 0-2.939-.353-4.227-1.058a8.04 8.04 0 0 1-3.098-3.026c-.754-1.336-1.13-2.903-1.13-4.702 0-2.162.473-4.108 1.42-5.831.972-1.751 2.285-3.112 3.938-4.083a10.386 10.386 0 0 1 5.468-1.495c3.013 0 5.139.984 6.379 2.951l.509-2.66h5.687ZM156.545 30.48c1.118 0 2.114-.268 2.988-.801a5.66 5.66 0 0 0 2.042-2.298c.487-.972.729-2.09.729-3.354 0-1.335-.401-2.393-1.202-3.17-.778-.778-1.872-1.167-3.282-1.167-1.118 0-2.114.28-2.988.838a5.41 5.41 0 0 0-2.041 2.26c-.487.972-.73 2.09-.73 3.354 0 1.337.389 2.393 1.166 3.172.802.777 1.908 1.166 3.318 1.166Zm24.912 4.701c-2.016 0-3.779-.363-5.286-1.092-1.507-.73-2.673-1.75-3.498-3.062-.802-1.336-1.205-2.88-1.205-4.63 0-2.162.511-4.108 1.533-5.831a10.934 10.934 0 0 1 4.264-4.083c1.823-.998 3.875-1.495 6.161-1.495 2.09 0 3.888.437 5.394 1.312a8.016 8.016 0 0 1 3.427 3.647l-4.811 2.404c-.852-1.775-2.309-2.66-4.374-2.66-1.118 0-2.128.278-3.026.837a5.657 5.657 0 0 0-2.078 2.26c-.487.972-.729 2.078-.729 3.318 0 1.337.401 2.405 1.202 3.208.802.777 1.92 1.166 3.354 1.166 2.09 0 3.693-.875 4.811-2.624l4.192 2.624c-.948 1.481-2.246 2.637-3.899 3.463-1.629.825-3.439 1.238-5.432 1.238Zm26.351-20.193c2.188 0 3.925.583 5.213 1.75 1.289 1.165 1.932 2.82 1.932 4.956 0 .78-.084 1.557-.255 2.334L212.51 34.89h-5.686l2.114-10.497a5.84 5.84 0 0 0 .146-1.348c0-1.022-.279-1.8-.838-2.335-.559-.533-1.398-.801-2.516-.801-1.457 0-2.673.425-3.645 1.276-.948.826-1.591 2.078-1.931 3.753l-1.968 9.952h-5.687l5.395-27.047h5.686l-1.822 9.223c1.676-1.385 3.693-2.078 6.05-2.078Z"
            ></path></svg
          >
        </div>
        <p class="header__description">на базе AI технологии JuniStat</p>
      </div>
    </div>
  </header>
  <section id="hero" class="overflow-hidden">
    <div class="container">
      <div class="hero">
        <h1 class="hero__headline">
          Система футбольных тестирований со смартфона
        </h1>
        <div class="hero__content">
          <Picture
            class="hero__image"
            src={HeroPhone}
            alt="Пример выполнения упражнения 'змейка'"
            formats={["webp", "png"]}
            widths={[540, 850]}
            loading={"eager"}
          />
          <div class="hero__rfs">
            <svg
              class="hero__rfs-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="144"
              height="144"
              fill="none"
              viewBox="0 0 144 144"
              ><path
                fill="#fff"
                d="m136.87 51.136-72.231 72.225a9.01 9.01 0 0 1-6.365 2.637 9.008 9.008 0 0 1-6.364-2.637l-40.287-40.5a9 9 0 0 1 0-12.718l13.5-13.5a9 9 0 0 1 12.696-.034l20.61 19.84.062.061 52.16-51.395a9.002 9.002 0 0 1 12.702 0l13.5 13.252a8.997 8.997 0 0 1 .017 12.769ZM89.611 126v-16.968h5.88c1.248 0 2.32.208 3.216.624.896.4 1.576 1.016 2.04 1.848.48.832.72 1.888.72 3.168 0 1.248-.28 2.304-.84 3.168-.544.848-1.296 1.488-2.256 1.92-.96.432-2.064.648-3.312.648H91.7V126h-2.088Zm2.088-7.488h3.264c1.296 0 2.344-.304 3.144-.912.816-.624 1.224-1.632 1.224-3.024 0-1.216-.328-2.128-.984-2.736-.656-.608-1.64-.912-2.952-.912H91.7v7.584Zm20.059 7.704v-2.76h-1.92c-1.28-.032-2.368-.336-3.264-.912a5.403 5.403 0 0 1-1.992-2.352c-.448-.992-.672-2.112-.672-3.36.016-1.312.296-2.44.84-3.384a5.634 5.634 0 0 1 2.232-2.184c.944-.512 2.016-.768 3.216-.768h1.56v-2.4h2.088v2.4h1.536c1.216 0 2.296.256 3.24.768a5.634 5.634 0 0 1 2.232 2.184c.544.944.824 2.072.84 3.384.016 1.248-.216 2.368-.696 3.36a5.615 5.615 0 0 1-2.088 2.352c-.896.576-1.984.88-3.264.912h-1.8v2.76h-2.088Zm-1.488-4.56h1.488v-9.36h-1.392c-.896 0-1.672.192-2.328.576a3.944 3.944 0 0 0-1.488 1.584c-.352.672-.528 1.448-.528 2.328 0 .928.144 1.76.432 2.496.288.736.744 1.32 1.368 1.752.624.416 1.44.624 2.448.624Zm3.576 0h1.488c1.008 0 1.824-.208 2.448-.624a3.729 3.729 0 0 0 1.368-1.752c.288-.736.432-1.568.432-2.496 0-.88-.176-1.656-.528-2.328a3.896 3.896 0 0 0-1.512-1.584c-.64-.384-1.408-.576-2.304-.576h-1.392v9.36Zm18.067 4.536c-1.744 0-3.2-.368-4.368-1.104-1.152-.752-2.016-1.776-2.592-3.072-.56-1.312-.84-2.808-.84-4.488 0-1.664.288-3.144.864-4.44.576-1.312 1.44-2.344 2.592-3.096 1.168-.768 2.632-1.152 4.392-1.152 1.248 0 2.32.2 3.216.6.912.384 1.648.92 2.208 1.608.56.688.944 1.496 1.152 2.424h-2.112c-.24-.832-.76-1.496-1.56-1.992-.784-.496-1.776-.744-2.976-.744-1.344 0-2.432.32-3.264.96-.816.624-1.416 1.448-1.8 2.472-.384 1.008-.576 2.104-.576 3.288 0 1.248.2 2.392.6 3.432.4 1.04 1.024 1.872 1.872 2.496.848.608 1.928.912 3.24.912.8 0 1.512-.112 2.136-.336.64-.224 1.168-.544 1.584-.96a3.25 3.25 0 0 0 .888-1.44h2.28a5.84 5.84 0 0 1-1.296 2.448c-.608.688-1.384 1.224-2.328 1.608-.944.384-2.048.576-3.312.576Z"
              ></path></svg
            >
            <p class="hero__rfs-text h3">
              Проверена специалистами РФС и применяется для комплексной оценки
              игроков
            </p>
            <a
              class="hero__rfs-letter-link"
              href={RfsLetter.src}
              target="_black"
              ><span style="text-decoration: underline"
                >Рекомендательное письмо</span
              > ↗</a
            >
          </div>
        </div>
      </div>
    </div>
  </section>
  <section id="tool" class="section-space">
    <div class="container">
      <div class="tool">
        <div class="tool__headline-wrap">
          <div class="tool__headline-icons is--mobile">
            <Image class="tool__headline-icon" src={IconRub} alt="" />
            <Image class="tool__headline-icon" src={IconStar} alt="" />
            <Image class="tool__headline-icon" src={IconRocket} alt="" />
          </div>
          <h2 class="tool__headline">
            <span class="hide-on-desktop"
              >Идеальный инструмент для дополнительного заработка школы,
              мотивации и прогресса юных футболистов</span
            >

            <span class="hide-on-mobile">
              Идеальный инструмент для дополнительного заработка<span
                >&nbsp;</span
              ><div class="tool__headline-icon-wrap">
                <Image class="tool__headline-icon" src={IconRub} alt="" />
              </div><span class="hide-on-mobile">&nbsp;</span>школы, мотивации<span
                >&nbsp;</span
              ><div class="tool__headline-icon-wrap">
                <Image class="tool__headline-icon" src={IconStar} alt="" />
              </div><span class="hide-on-mobile">&nbsp;</span>и прогресса<span
                >&nbsp;</span
              ><div class="tool__headline-icon-wrap">
                <Image class="tool__headline-icon" src={IconRocket} alt="" />
              </div>юных футболистов
            </span>
          </h2>
        </div>
        <div class="tool__points">
          <h4 class="tool__point">
            Приобретите необходимое количество тестов или безлимитных лицензий
            для игроков
          </h4>
          <h4 class="tool__point">
            Включите стоимость системы в абонемент по своему собственному тарифу
          </h4>
        </div>
        <RegisterButton id="tool-registration" class="tool__register-button" />
      </div>
    </div>
  </section>
  <section id="benefits" class="section-space">
    <div class="container">
      <div class="benefits">
        <h2 class="benefits__headline">Что вы получаете от ЮниКоуч</h2>
        <div class="benefits__content">
          <div class="benefits__cards">
            <div class="benefits__card">
              <Picture
                class="benefits__players-cards is--mobile"
                src={PlayersCards}
                alt="Карточки игроков"
              />
              <svg
                class="benefits__card-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="129"
                height="128"
                fill="none"
                viewBox="0 0 129 128"
                ><path
                  fill="#FFE83F"
                  d="m20.56 15.07 43.823-9.738 43.823 9.739a5.333 5.333 0 0 1 4.177 5.206V73.54a31.998 31.998 0 0 1-14.25 26.625l-33.75 22.5-33.75-22.5a32 32 0 0 1-14.25-26.626V20.278c0-2.5 1.736-4.664 4.176-5.206Zm43.823 43.595c7.364 0 13.333-5.97 13.333-13.333 0-7.364-5.97-13.333-13.333-13.333-7.364 0-13.333 5.97-13.333 13.333 0 7.364 5.97 13.333 13.333 13.333ZM40.529 85.332h47.707c-1.326-12-11.5-21.333-23.853-21.333-12.354 0-22.527 9.333-23.854 21.333Z"
                ></path></svg
              >

              <p class="benefits__description h4">
                <b>Игроки</b> — цифровые карточки как у настоящих футболистов
                в FIFA с точно посчитанными параметрами силы, ловкости,
                скорости, ударов и другими метриками
              </p>
            </div>
            <div class="benefits__card">
              <svg
                class="benefits__card-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="129"
                height="128"
                fill="none"
                viewBox="0 0 129 128"
                ><path
                  fill="#FFE83F"
                  d="m85.716 10.666 26.667 26.667v74.71a5.294 5.294 0 0 1-5.298 5.29H21.681c-2.926 0-5.298-2.429-5.298-5.29V15.956a5.295 5.295 0 0 1 5.298-5.29h64.035ZM59.05 37.333v53.333h10.666V37.333H59.05Zm21.333 21.333v32h10.666v-32H80.383ZM37.716 69.333v21.333h10.667V69.333H37.716Z"
                ></path></svg
              >

              <p class="benefits__description h4">
                <b>Родители</b> — рекомендации по развитию, а <b>тренеры</b> —
                рекомендации по тренировкам
              </p>
            </div>
            <div class="benefits__card">
              <svg
                class="benefits__card-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="129"
                height="128"
                fill="none"
                viewBox="0 0 129 128"
                ><path
                  fill="#FFE83F"
                  d="M34.669 114.999v-12.796H21V89.116h13.669v-10.76H21V64.832h13.669V12.193h35.916c5.72 0 10.858.582 15.414 1.745 4.556 1.164 8.434 3.054 11.633 5.671 3.296 2.618 5.816 6.01 7.561 10.18 1.842 4.168 2.763 9.209 2.763 15.122 0 7.755-1.648 14.105-4.944 19.049-3.296 4.944-7.803 8.58-13.523 10.906-5.72 2.326-12.166 3.49-19.34 3.49H50.082v10.76h33.881v13.087h-33.88v12.796H34.668Zm15.413-50.167h19.485c4.266 0 8.095-.63 11.488-1.89 3.49-1.26 6.253-3.344 8.288-6.253 2.133-3.005 3.2-6.883 3.2-11.633 0-6.785-1.842-11.584-5.526-14.395-3.684-2.909-9.21-4.363-16.577-4.363H50.082v38.534Z"
                ></path></svg
              >
              <p class="benefits__description h4">
                <b>Школы и академии</b> — дополнительные доходы и удобный инструмент
                тестирования
              </p>
            </div>
            <div class="benefits__card">
              <svg
                class="benefits__card-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="129"
                height="128"
                fill="none"
                viewBox="0 0 129 128"
                ><path
                  fill="#FFE83F"
                  d="m96.546 88.624 22.841 22.841-7.543 7.543-22.84-22.841a47.792 47.792 0 0 1-29.957 10.501c-26.496 0-48-21.504-48-48s21.504-48 48-48 48 21.504 48 48a47.791 47.791 0 0 1-10.501 29.956Zm-10.7-3.957c6.52-6.721 10.534-15.89 10.534-25.999 0-20.627-16.706-37.333-37.333-37.333S21.714 38.04 21.714 58.668 38.42 96.001 59.047 96.001c10.11 0 19.278-4.013 25.999-10.535l.8-.8ZM65.328 38.275A10.669 10.669 0 0 0 59.047 48c0 5.892 4.775 10.667 10.666 10.667a10.67 10.67 0 0 0 9.728-6.282c.61 1.986.94 4.095.94 6.282C80.381 70.449 70.829 80 59.047 80s-21.333-9.55-21.333-21.332 9.55-21.333 21.333-21.333c2.187 0 4.296.328 6.282.94Z"
                ></path></svg
              >
              <p class="benefits__description h4">
                Игроки с высокими результатами тестов попадают в поле зрения
                клубов и скаутов и получают приглашения на просмотры
              </p>
              <p class="small-text">
                Можно скрыть профили игроков, если не хотите показывать
              </p>
            </div>
            <RegisterButton
              id="benefits-registration"
              class:list="benefits__button"
              arrow
            />
          </div>
          <Picture
            class="benefits__players-cards"
            src={PlayersCards}
            alt="Карточки игроков"
          />
        </div>
      </div>
    </div>
  </section>

  <section id="tests" class="section-space overflow-hidden">
    <div class="container">
      <div class="tests__head">
        <div class="tests__head-title-wrap">
          <p class="tests__top-description">Для футболистов от 8 до 21 года</p>
          <h2 class="tests__headline">Тесты и метрики</h2>
          <p class="h3 tests__description">
            14 тестов, которые точно оценивают 70+ технических и физических
            характеристик
          </p>
        </div>
        <div class="tests__navigation-wrap">
          <div class="tests__navigation">
            <button class="button tests__navigation-button is--prev"
              >Назад</button
            >
            <p class="tests__navigation-counter p2">0 / 00</p>
            <button class="button tests__navigation-button is--next"
              >Вперёд</button
            >
          </div>
        </div>
      </div>
    </div>

    <div class="container is--tests swiper">
      <div class="tests is--preview swiper-wrapper">
        <!-- Tests -->
        <TestCard
          lang="ru"
          testName="Бег 15м со старта"
          type="large"
          video={m15SprintMP4}
          categories={["pace", "agility"]}
          icon={Run15Icon}
          poster={optimizedPosters["Sprint15Poster"].src}
        />
        <!-- 3 cards -->
        <div class="swiper-slide tests__slide-group is--1">
          <TestCard
            lang="ru"
            testName="Лесенка лево-право"
            type="small"
            video={ladderLRMP4}
            categories={["pace", "agility"]}
            icon={LadderLRIcon}
            poster={optimizedPosters["LadderLRPoster"].src}
          />
          <TestCard
            lang="ru"
            testName="Лесенка вперед-назад"
            type="small"
            video={ladderBFMP4}
            categories={["pace", "agility"]}
            icon={LadderFBIcon}
            poster={optimizedPosters["LadderBFPoster"].src}
          />
          <TestCard
            lang="ru"
            testName="Прыжок вверх с места"
            type="small"
            video={highJumpMP4}
            categories={["physical", "agility"]}
            icon={jumpIcon}
            poster={optimizedPosters["JumpPoster"].src}
          />
        </div>
        <!-- 3 cards End-->

        <!-- 2 cards -->
        <div class="swiper-slide tests__slide-group is--2">
          <TestCard
            lang="ru"
            testName="Змейка"
            type="medium"
            video={serpentMP4}
            categories={["dribbling"]}
            icon={SerpentIcon}
            poster={optimizedPosters["SerpentPoster"].src}
          />
          <TestCard
            lang="ru"
            testName="Пушечный удар правой/левой ногой"
            type="medium"
            video={powerKickMP4}
            categories={["shooting"]}
            icon={KickIcon}
            poster={optimizedPosters["KickPoster"].src}
          />
        </div>
        <!-- 2 cards End-->
        <TestCard
          lang="ru"
          testName="Бег на максимальной скорости 15м"
          type="large"
          video={m15SprintFromRunMP4}
          categories={["pace"]}
          icon={Run15upIcon}
          poster={optimizedPosters["Run15UpPoster"].src}
        />
        <TestCard
          lang="ru"
          testName="Стрела"
          type="large"
          video={arrowMP4}
          categories={["agility"]}
          icon={ArrowIcon}
          poster={optimizedPosters["ArrowPoster"].src}
        />
        <TestCard
          lang="ru"
          testName="Ведение 15м правой/левой с ход"
          type="large"
          video={dribblingMP4}
          categories={["dribbling"]}
          icon={Dribbling15Icon}
          poster={optimizedPosters["DribblingPoster"].src}
        />
        <TestCard
          lang="ru"
          testName="Стрела с мячом правой/левой ногой"
          type="large"
          video={arrowWithBallMP4}
          categories={["dribbling"]}
          icon={ArrowBallIcon}
          poster={optimizedPosters["ArrowBallPoster"].src}
        />
        <TestCard
          lang="ru"
          testName="Отжимания"
          type="large"
          video={pushUpsMP4}
          categories={["physical"]}
          icon={PushUpsIcon}
          poster={optimizedPosters["PushUpsPoster"].src}
        />

        <!-- Tests End -->
      </div>
      <div class="tests__navigation-wrap is--mobile">
        <div class="tests__navigation">
          <button class="button tests__navigation-button is--prev">Назад</button
          >
          <p class="tests__navigation-counter p2">0 / 00</p>
          <button class="button tests__navigation-button is--next"
            >Вперёд</button
          >
        </div>
      </div>
      <div class="tests__pagination is--bullets"></div>
    </div>
  </section>
  <section id="price" class="section-space">
    <div class="container">
      <div class="price">
        <h2 class="price__headline">Цены</h2>
        <div class="price__grid">
          <div class="price__plan">
            <h3 class="price__plan-title">Потестовая оплата</h3>
            <div class="price__plan-price-wrap">
              <p class="price__plan-price h4">
                <span class="price__plan-price-sum h3">100 ₽</span> / за 1 тест
              </p>
            </div>
            <p class="price__plan-description">
              Оптимально для скаутов или индивидуальных тренеров. Пополняйте
              баланс тестов и тестируйте игроков на турнирах или тренировках
            </p>
          </div>
          <div class="price__plan">
            <h3 class="price__plan-title">День тестирований</h3>
            <div class="price__plan-price-wrap">
              <p class="price__plan-price h4">
                <span class="price__plan-price-sum h3"
                  >{prices?.day ?? "1 500"} ₽</span
                > / за игрока
              </p>
            </div>
            <p class="price__plan-description">
              Любое количество тестов.<br />Оптимально для школ и скаутов,
              которые проводят тесты от одного до пяти раз в год.
              Можно использовать тесты для дополнительного заработка
            </p>
          </div>
          <div class="price__plan is--big">
            <h3 class="price__plan-title">Безлимитный доступ</h3>
            <div class="price__plan-price-wrap">
              <div class="price__plan-price-row">
                <p class="price__plan-price h4">
                  <span class="price__plan-price-sum h3"
                    >{prices?.month ?? "3000"} ₽</span
                  > / 4 месяца за игрока
                </p>
                <p class="p2" style="margin-top: 8px">
                  Можно использовать сервис любые 4 месяца в году
                </p>
              </div>

              <div class="price__plan-price-row">
                <p class="price__plan-price h4">
                  <span class="price__plan-price-sum h3"
                    >{prices?.year ?? "5 000"} ₽</span
                  > / год за игрока
                </p>
                <p class="price__plan-description" style="margin-top: 8px">
                  Для максимального использования возможностей системы
                  и дополнительного заработка для школ
                </p>
              </div>
            </div>

            <hr />
            <ul class="price__plan-points">
              <li class="price__plan-point">
                Виджет базы талантов на сайт вашей школы
              </li>
              <li class="price__plan-point">
                Продвинутые рекомендации, отчёты и аналитика — 200 ₽ за игрока
                после 3х месяцев тестов
              </li>
            </ul>
          </div>
          <div class="price__plan is--special">
            <h3 class="price__plan-title">Индивидуальные условия</h3>
            <h4 class="price__plan-description">
              Вам требуется специальный тариф или есть другие вопросы?<br
              />Свяжитесь с нами <a
                class="price__link"
                href="mailto:<EMAIL>"
                target="_blank"><EMAIL></a
              >
            </h4>
          </div>
        </div>
        <RegisterButton
          id="price-registration"
          class="price__register-button"
        />
      </div>
    </div>
  </section>

  <section class="section-space">
    <div class="container">
      <div class="help">
        <h2 class="help__headline">Инструкции и руководства</h2>
        <div class="price__grid">
          <a
            class="price__plan help__card"
            href="/ru/hc/articles/setup-academy"
            target="_blank"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="112"
              height="112"
              fill="none"
              viewBox="0 0 112 112"
              ><path
                fill="var(--primary)"
                d="M104.099 46.906a3.503 3.503 0 0 0-1.702-2.363l-13.05-7.437-.053-14.71a3.5 3.5 0 0 0-1.239-2.66 48.962 48.962 0 0 0-16.065-9.042 3.5 3.5 0 0 0-2.826.258L56 18.31l-13.178-7.372a3.502 3.502 0 0 0-2.83-.263 48.967 48.967 0 0 0-16.048 9.078 3.5 3.5 0 0 0-1.238 2.656l-.066 14.722-13.05 7.437a3.501 3.501 0 0 0-1.702 2.363 46.58 46.58 0 0 0 0 18.182 3.5 3.5 0 0 0 1.702 2.363l13.05 7.437.053 14.713a3.5 3.5 0 0 0 1.238 2.66 48.97 48.97 0 0 0 16.065 9.044 3.51 3.51 0 0 0 2.826-.259L56 93.692l13.177 7.372c.522.29 1.11.441 1.707.437a3.52 3.52 0 0 0 1.124-.184 49.035 49.035 0 0 0 16.047-9.069 3.5 3.5 0 0 0 1.239-2.656l.065-14.721 13.051-7.438a3.51 3.51 0 0 0 1.702-2.362 46.603 46.603 0 0 0-.013-18.165ZM55.999 73.5a17.5 17.5 0 1 1 0-34.998A17.5 17.5 0 0 1 56 73.5Z"
              ></path></svg
            >
            <h4 class="price__plan-title">Настройка Академии</h4>
          </a>
          <a
            class="price__plan help__card"
            href="/ru/hc/articles/become-representative"
            target="_blank"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="112"
              height="112"
              fill="none"
              viewBox="0 0 112 112"
              ><path
                fill="var(--primary)"
                d="M56 33.25a19.25 19.25 0 1 1 0 38.499 19.25 19.25 0 0 1 0-38.499ZM77 28h7v7a3.5 3.5 0 1 0 7 0v-7h7a3.499 3.499 0 1 0 0-7h-7v-7a3.5 3.5 0 0 0-7 0v7h-7a3.5 3.5 0 1 0 0 7Zm19.841 17.548a3.5 3.5 0 0 0-2.874 4.03c.355 2.122.534 4.27.533 6.422a38.334 38.334 0 0 1-9.73 25.554 34.94 34.94 0 0 0-9.52-9.323 1.75 1.75 0 0 0-2.117.14 26.167 26.167 0 0 1-34.243 0 1.75 1.75 0 0 0-2.14-.14 34.875 34.875 0 0 0-9.533 9.323A38.5 38.5 0 0 1 56 17.5c2.152 0 4.3.178 6.423.534a3.5 3.5 0 0 0 1.155-6.904 45.465 45.465 0 1 0 37.292 37.293 3.497 3.497 0 0 0-4.029-2.875Z"
              ></path></svg
            >
            <h4 class="price__plan-title">Подключение представителя</h4>
          </a>
          <a
            class="price__plan help__card"
            href="https://www.youtube.com/playlist?list=PLkknS-3qPsQfQgp2wJGYITFOtiau6sL63"
            target="_blank"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="112"
              height="112"
              fill="none"
              viewBox="0 0 112 112"
              ><path
                fill="var(--primary)"
                d="M101.5 91a3.501 3.501 0 0 1-3.5 3.5H14a3.5 3.5 0 1 1 0-7h84a3.5 3.5 0 0 1 3.5 3.5Zm0-66.5v49a6.998 6.998 0 0 1-7 7h-77a7 7 0 0 1-7-7v-49a7 7 0 0 1 7-7h77a7 7 0 0 1 7 7ZM71.75 49a3.5 3.5 0 0 0-1.492-2.866l-17.5-12.25a3.5 3.5 0 0 0-5.508 2.866v24.5a3.5 3.5 0 0 0 5.508 2.866l17.5-12.25A3.5 3.5 0 0 0 71.75 49Z"
              ></path></svg
            >
            <h4 class="price__plan-title">
              Видео-инструкции для приложения JuniCoach
            </h4>
          </a>
        </div>
      </div>
    </div>
  </section>

  <footer>ООО «ЮниСтат», Москва</footer>

  <script>
    const matchMobile = window.matchMedia(
      "screen and (max-width: 992px)",
    ).matches;

    //#region [Slider]
    import { Swiper } from "swiper";
    import { Navigation, Pagination } from "swiper/modules";
    // import Swiper and modules styles

    const testsSlider = new Swiper(".container.swiper", {
      modules: [Navigation, Pagination],
      slidesPerView: "auto",
      slideToClickedSlide: true,
      autoHeight: true,
      init: false,
      spaceBetween: 0,

      breakpoints: {
        320: {
          spaceBetween: 16,
        },

        992: {
          spaceBetween: 0,
        },
      },

      // If we need pagination
      pagination: {
        el: ".tests__pagination.is--bullets",
        type: "bullets",
        clickable: true,
      },

      on: {
        activeIndexChange: countTestsSlides,
        init: countTestsSlides,
        slideChange: () => {
          prevSlideButton.forEach((button) => {
            checkNavButton(button, "prev");
          });
          nextSlideButton.forEach((button) => {
            checkNavButton(button, "next");
          });
        },
      },
    });

    const nextSlideButton = document.querySelectorAll(
      ".tests__navigation-button.is--next",
    ) as NodeListOf<HTMLElement>;
    const prevSlideButton = document.querySelectorAll(
      ".tests__navigation-button.is--prev",
    ) as NodeListOf<HTMLElement>;

    function checkNavButton(button: HTMLElement, direction: "prev" | "next") {
      switch (true) {
        case testsSlider.isEnd:
          button.classList.add("swiper-button-disabled");
          button.setAttribute("disabled", "true");

          prevSlideButton.forEach((button) => {
            button.classList.remove("swiper-button-disabled");
            button.removeAttribute("disabled");
          });
          break;
        case testsSlider.isBeginning:
          button.classList.add("swiper-button-disabled");
          button.setAttribute("disabled", "true");

          nextSlideButton.forEach((button) => {
            button.classList.remove("swiper-button-disabled");
            button.removeAttribute("disabled");
          });
          break;
        default:
          prevSlideButton.forEach((button) => {
            button.classList.remove("swiper-button-disabled");
            button.removeAttribute("disabled");
          });
          nextSlideButton.forEach((button) => {
            button.classList.remove("swiper-button-disabled");
            button.removeAttribute("disabled");
          });
      }
    }

    nextSlideButton.forEach((button) => {
      button.addEventListener("click", () => {
        testsSlider.slideNext();
        checkNavButton(button, "next");
      });
    });

    prevSlideButton.forEach((button) => {
      if (testsSlider.isBeginning) {
        button.classList.add("swiper-button-disabled");
        button.setAttribute("disabled", "true");
      }

      button.addEventListener("click", () => {
        testsSlider.slidePrev();
        checkNavButton(button, "prev");
      });
    });

    function countTestsSlides() {
      const counters = document.querySelectorAll(
        ".tests__navigation-counter",
      ) as NodeListOf<HTMLElement>;
      counters.forEach((counter) => {
        counter.innerText = `${testsSlider.activeIndex + 1} / ${
          testsSlider.slides.length
        }`;
      });
    }

    if (matchMobile) {
      const slideGroup = document.querySelectorAll(
        ".swiper-slide.tests__slide-group",
      ) as NodeListOf<HTMLElement>;

      slideGroup.forEach((group) => {
        const slides = group.querySelectorAll(".swiper-slide");
        slides.forEach((slide) => {
          group.insertAdjacentElement("beforebegin", slide);
        });
        group.remove();
      });
    }

    testsSlider.init();
    //#endregion [Slider]

    //#region [Animation]
    import gsap from "gsap";
    import ScrollTrigger from "gsap/ScrollTrigger";
    gsap.registerPlugin(ScrollTrigger);

    //#region [Hero]

    let mm = gsap.matchMedia();

    mm.add("(max-width: 460px)", () => {
      gsap
        .timeline({
          scrollTrigger: {
            trigger: "#hero",
            start: "50% 30%",
            end: "150% bottom",
            scrub: 1,
          },
        })
        .to(".hero__image", {
          x: "-30%",
        });
    });

    //#endregion [Hero]

    //#region [Tool]
    gsap
      .timeline({
        scrollTrigger: {
          trigger: ".tool__headline-wrap",
          start: "top 80%",
          end: "bottom bottom",
          toggleActions: "play none play play", // onEnter, onLeave, onEnterBack, and onLeaveBack
        },
      })
      .from(".tool__headline-icon-wrap", {
        scale: 0,
        stagger: 0.2,
      });
    //#endregion [Tool]

    //#region [Benefits]
    document.querySelectorAll(".benefits__card").forEach((card) => {
      gsap
        .timeline({
          scrollTrigger: {
            trigger: card,
            start: "top 80%",
            end: "bottom bottom",
          },
        })
        .from(card, { autoAlpha: 0, y: "10%", duration: 0.4 });
    });

    //#endregion [Benefits]

    //#region [Price]
    document.querySelectorAll(".price__plan").forEach((card) => {
      gsap
        .timeline({
          scrollTrigger: {
            trigger: card,
            start: "top 80%",
            end: "bottom bottom",
          },
        })
        .from(card, { autoAlpha: 0, y: "10%" });
    });
    //#endregion [Price]
    //#endregion [Animation]

    //#region [Analytics]
    import { track } from "@amplitude/analytics-browser";

    document
      .querySelectorAll(
        "#tool-registration, #benefits-registration, #price-registration",
      )
      .forEach((button) => {
        button.addEventListener("click", (e) => {
          track("Click registration JuniCoach page", {
            // @ts-ignore
            place: e.currentTarget.id,
          });
        });
      });

    //#endregion [Analytics]
  </script>
</Layout>

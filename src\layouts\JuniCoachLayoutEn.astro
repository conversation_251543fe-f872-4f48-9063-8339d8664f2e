---
//#region [Built-in components]
import { Image, Picture, getImage } from "astro:assets";
//#endregion [Built-in components]

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

//#region [Components]
import Layout from "@layouts/Layout.astro";
import RegisterButton from "@components/junicoach/RegisterButton.astro";
import TestCard from "@components/ai-test/TestCard.astro";

//#endregion [Components]

//#region [Styles]
import "@styles/pages/junicoach-en.css";
//#endregion [Styles]

//#region [Images]
import HeroPhone from "@assets/images/junicoach/en/junicoach-record-en.png";

import IconRub from "@assets/images/junicoach/icon-rub.png";
import IconStar from "@assets/images/junicoach/icon-star.png";
import IconRocket from "@assets/images/junicoach/icon-rocket.png";

import PlayersCards from "@assets/images/junicoach/en/players-cards.png";

// Tests icons
import Run15upIcon from "@assets/images/ai-test/tests-icons/15m-run-up.jpg";
import PushUpsIcon from "@assets/images/ai-test/tests-icons/push-ups.jpg";
import ArrowBallIcon from "@assets/images/ai-test/tests-icons/arrow-ball.jpg";
import ArrowIcon from "@assets/images/ai-test/tests-icons/arrow.jpg";
import Dribbling15Icon from "@assets/images/ai-test/tests-icons/dribbling15-ball.jpg";
import jumpIcon from "@assets/images/ai-test/tests-icons/jump-place.jpg";
import LadderFBIcon from "@assets/images/ai-test/tests-icons/ladder-front-back.jpg";
import LadderLRIcon from "@assets/images/ai-test/tests-icons/ladder-left-right.jpg";
import KickIcon from "@assets/images/ai-test/tests-icons/monster-kick-right.jpg";
import Run15Icon from "@assets/images/ai-test/tests-icons/run-15.jpg";
import SerpentIcon from "@assets/images/ai-test/tests-icons/serpient-ball.jpg";

// Posters
import Run15UpPoster from "@assets/images/ai-test/video-posters/poster_15mRunUp-min.jpg";
import Sprint15Poster from "@assets/images/ai-test/video-posters/poster_15mSprint-min.jpg";
import ArrowPoster from "@assets/images/ai-test/video-posters/poster_arrow-min.jpg";
import ArrowBallPoster from "@assets/images/ai-test/video-posters/poster_arrowBall-min.jpg";
import DribblingPoster from "@assets/images/ai-test/video-posters/poster_dribbling-min.jpg";
import JumpPoster from "@assets/images/ai-test/video-posters/poster_jump-min.jpg";
import KickPoster from "@assets/images/ai-test/video-posters/poster_kick-min.jpg";
import LadderBFPoster from "@assets/images/ai-test/video-posters/poster_ladderBF-min.jpg";
import LadderLRPoster from "@assets/images/ai-test/video-posters/poster_ladderLR-min.jpg";
import PushUpsPoster from "@assets/images/ai-test/video-posters/poster_pushups-min.jpg";
import SerpentPoster from "@assets/images/ai-test/video-posters/poster_serpent-min.jpg";

const posters = {
  Run15UpPoster: Run15UpPoster,
  Sprint15Poster: Sprint15Poster,
  ArrowPoster: ArrowPoster,
  ArrowBallPoster: ArrowBallPoster,
  DribblingPoster: DribblingPoster,
  JumpPoster: JumpPoster,
  KickPoster: KickPoster,
  LadderBFPoster: LadderBFPoster,
  LadderLRPoster: LadderLRPoster,
  PushUpsPoster: PushUpsPoster,
  SerpentPoster: SerpentPoster,
};

const optimizedPosters: any = {};
for (const [key, value] of Object.entries(posters)) {
  const optimizedPoster = await getImage({ src: value, format: "webp" });
  optimizedPosters[key] = optimizedPoster;
}

//#endregion [Images]

//#region [Videos]
import m15SprintMP4 from "@assets/videos/ai-tests/15m-sprint.mp4";
import m15SprintFromRunMP4 from "@assets/videos/ai-tests/15m-from-run-up.mp4";
import arrowWithBallMP4 from "@assets/videos/ai-tests/arrow-with-ball.mp4";
import arrowMP4 from "@assets/videos/ai-tests/arrow.mp4";
import dribblingMP4 from "@assets/videos/ai-tests/dribbling.mp4";
import highJumpMP4 from "@assets/videos/ai-tests/jump.mp4";
import ladderBFMP4 from "@assets/videos/ai-tests/ladder-b-f.mp4";
import ladderLRMP4 from "@assets/videos/ai-tests/ladder-l-r.mp4";
import powerKickMP4 from "@assets/videos/ai-tests/power-kick.mp4";
import pushUpsMP4 from "@assets/videos/ai-tests/push-ups.mp4";
import serpentMP4 from "@assets/videos/ai-tests/serpent.mp4";
//#endregion [Videos]

interface Props {
  prices?: { day: number; month: number; year: number };
}
const { prices } = Astro.props;
---

<Layout
  contentOnly
  title="JuniCoach — Smartphone-based AI soccer testing"
  description="Offers a perfect tool for schools to earn extra income, motivate young footballers, and help them progress"
  ogImage="https://junistat.com/og-junicoach-en.jpg"
>
  <header>
    <div class="container">
      <div class="header">
        <div class="header__logo-wrap">
          <svg
            class="header__logo"
            xmlns="http://www.w3.org/2000/svg"
            width="227"
            height="42"
            fill="none"
            viewBox="0 0 227 42"
            ><path
              fill="#FF651D"
              d="M92.736 4.18A5.15 5.15 0 0 1 97.795 0h123.347a5.145 5.145 0 0 1 3.984 1.888 5.148 5.148 0 0 1 1.063 4.278l-6.4 31.704a5.147 5.147 0 0 1-5.047 4.13H91.722a5.15 5.15 0 0 1-5.06-6.118L92.737 4.18ZM8.311 36.014c-1.943 0-3.62-.327-5.03-.984C1.872 34.373.778 33.426 0 32.187l3.901-3.719c1.118 1.8 2.613 2.698 4.484 2.698 2.284 0 3.693-1.325 4.228-3.974L15.056 14.8h-8.93l.947-4.739h14.8L18.481 26.9c-.631 3.258-1.775 5.59-3.426 7-1.653 1.41-3.901 2.114-6.745 2.114Zm37.547-20.049-3.901 19.612h-5.394l.4-2.115a8.67 8.67 0 0 1-2.916 1.823c-1.112.393-2.284.59-3.463.585-2.138 0-3.851-.585-5.14-1.751-1.264-1.166-1.895-2.807-1.895-4.921 0-.802.084-1.591.255-2.37l2.151-10.863h5.687l-2.078 10.498a6.826 6.826 0 0 0-.146 1.422c0 2.042 1.094 3.062 3.28 3.062 1.434 0 2.613-.413 3.536-1.238.948-.852 1.591-2.116 1.932-3.791l2.006-9.953h5.686Zm16.678-.292c2.186 0 3.923.585 5.211 1.751 1.289 1.142 1.932 2.781 1.932 4.92a11 11 0 0 1-.255 2.37l-2.186 10.863H61.55l2.116-10.499c.1-.442.148-.895.144-1.348 0-1.02-.279-1.798-.838-2.333-.559-.535-1.396-.801-2.514-.801-1.459 0-2.673.425-3.647 1.274-.947.826-1.59 2.078-1.931 3.755l-1.968 9.952h-5.687l3.9-19.612h5.396l-.437 2.116c1.749-1.605 3.899-2.408 6.452-2.408Zm13.793.292h5.65l-3.901 19.612h-5.65l3.9-19.612Zm3.79-2.734c-.971 0-1.773-.28-2.406-.838a2.825 2.825 0 0 1-.91-2.114c0-.972.34-1.773 1.02-2.406.706-.656 1.615-.984 2.734-.984.997 0 1.799.28 2.406.84.631.532.948 1.201.948 2.003 0 1.046-.353 1.896-1.058 2.553-.704.63-1.615.946-2.733.946Z"
            ></path><path
              fill="#fff"
              d="M109.808 35.326c-2.55 0-4.786-.46-6.706-1.384-1.896-.924-3.354-2.224-4.374-3.901-1.022-1.676-1.533-3.609-1.533-5.797 0-2.89.657-5.49 1.97-7.799 1.336-2.334 3.208-4.167 5.612-5.504 2.407-1.336 5.166-2.005 8.275-2.005 2.285 0 4.302.388 6.051 1.166 1.75.777 3.074 1.895 3.974 3.354l-4.264 3.499c-1.362-1.992-3.414-2.988-6.161-2.988-1.872 0-3.525.437-4.958 1.31a9.127 9.127 0 0 0-3.354 3.573c-.777 1.507-1.166 3.184-1.166 5.031 0 1.968.619 3.537 1.86 4.703 1.264 1.142 3.025 1.713 5.285 1.713 2.697 0 4.957-.984 6.781-2.953l3.498 3.572c-2.527 2.941-6.123 4.41-10.79 4.41Zm22.523-.146c-1.97 0-3.707-.363-5.214-1.092-1.483-.73-2.637-1.75-3.462-3.062-.802-1.336-1.204-2.88-1.204-4.63 0-2.162.511-4.108 1.532-5.831a11.008 11.008 0 0 1 4.228-4.083c1.798-.998 3.827-1.495 6.087-1.495 1.994 0 3.731.364 5.214 1.094 1.481.73 2.623 1.761 3.426 3.098.826 1.312 1.239 2.843 1.239 4.592 0 2.164-.511 4.108-1.531 5.833-.996 1.725-2.407 3.086-4.228 4.083-1.799.996-3.827 1.493-6.087 1.493Zm.364-4.7c1.093 0 2.078-.269 2.952-.802a5.682 5.682 0 0 0 2.042-2.298c.485-.972.729-2.09.729-3.354 0-1.335-.389-2.393-1.166-3.17-.779-.778-1.872-1.167-3.282-1.167-1.118 0-2.114.28-2.988.838a5.42 5.42 0 0 0-2.042 2.26c-.486.972-.729 2.09-.729 3.354 0 1.337.389 2.393 1.166 3.172.802.777 1.908 1.166 3.318 1.166Zm37.045-15.203-3.9 19.612h-5.395l.364-1.931c-1.627 1.483-3.632 2.224-6.015 2.224-1.53 0-2.939-.353-4.227-1.058a8.04 8.04 0 0 1-3.098-3.026c-.754-1.336-1.13-2.903-1.13-4.702 0-2.162.473-4.108 1.42-5.831.972-1.751 2.285-3.112 3.938-4.083a10.386 10.386 0 0 1 5.468-1.495c3.013 0 5.139.984 6.379 2.951l.509-2.66h5.687ZM156.545 30.48c1.118 0 2.114-.268 2.988-.801a5.66 5.66 0 0 0 2.042-2.298c.487-.972.729-2.09.729-3.354 0-1.335-.401-2.393-1.202-3.17-.778-.778-1.872-1.167-3.282-1.167-1.118 0-2.114.28-2.988.838a5.41 5.41 0 0 0-2.041 2.26c-.487.972-.73 2.09-.73 3.354 0 1.337.389 2.393 1.166 3.172.802.777 1.908 1.166 3.318 1.166Zm24.912 4.701c-2.016 0-3.779-.363-5.286-1.092-1.507-.73-2.673-1.75-3.498-3.062-.802-1.336-1.205-2.88-1.205-4.63 0-2.162.511-4.108 1.533-5.831a10.934 10.934 0 0 1 4.264-4.083c1.823-.998 3.875-1.495 6.161-1.495 2.09 0 3.888.437 5.394 1.312a8.016 8.016 0 0 1 3.427 3.647l-4.811 2.404c-.852-1.775-2.309-2.66-4.374-2.66-1.118 0-2.128.278-3.026.837a5.657 5.657 0 0 0-2.078 2.26c-.487.972-.729 2.078-.729 3.318 0 1.337.401 2.405 1.202 3.208.802.777 1.92 1.166 3.354 1.166 2.09 0 3.693-.875 4.811-2.624l4.192 2.624c-.948 1.481-2.246 2.637-3.899 3.463-1.629.825-3.439 1.238-5.432 1.238Zm26.351-20.193c2.188 0 3.925.583 5.213 1.75 1.289 1.165 1.932 2.82 1.932 4.956 0 .78-.084 1.557-.255 2.334L212.51 34.89h-5.686l2.114-10.497a5.84 5.84 0 0 0 .146-1.348c0-1.022-.279-1.8-.838-2.335-.559-.533-1.398-.801-2.516-.801-1.457 0-2.673.425-3.645 1.276-.948.826-1.591 2.078-1.931 3.753l-1.968 9.952h-5.687l5.395-27.047h5.686l-1.822 9.223c1.676-1.385 3.693-2.078 6.05-2.078Z"
            ></path></svg
          >
        </div>
        <p class="header__description">powered by JuniStat AI technology</p>
      </div>
    </div>
  </header>
  <section id="hero" class="overflow-hidden">
    <div class="container">
      <div class="hero">
        <h1 class="hero__headline">Smartphone-based AI soccer testing</h1>
        <div class="hero__content">
          <Picture
            class="hero__image"
            src={HeroPhone}
            alt="Пример выполнения упражнения 'змейка'"
            formats={["webp", "png"]}
            widths={[540, 850]}
            loading={"eager"}
          />
          <div class="hero__rfs">
            <svg
              class="hero__rfs-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="119"
              height="144"
              fill="none"
              viewBox="0 0 119 144"
              ><path
                fill="#fff"
                d="M59.59.156c1.136.565 2.203 1.405 3.26 2.11l5.382 3.41c9.33 5.526 19.249 9.4 29.962 11.238a96.164 96.164 0 0 0 10.678 1.23l3.937.195 2.749.016c.522.004 1.086-.033 1.591.109.593.166 1.152.701 1.427 1.244.327.643.268 1.403.266 2.103l-.005 2.658-.004 6.573.033 15.149-.136 12.073c-.572 9.891-2.57 19.668-5.874 29.002-1.385 3.913-3.07 7.678-4.947 11.376-3.863 7.612-8.665 14.642-14.443 20.927-9.422 10.251-21.349 18.188-33.837 24.173-.116.012-.126.02-.258-.003-.474-.084-1.17-.558-1.614-.779l-6.166-3.223c-16.353-9.218-30.513-22.644-39.49-39.246C4.908 87.188.953 72.599.251 57.494l-.072-9.551.002-13.179-.008-9.282v-3.267c0-.727-.064-1.533.154-2.233.239-.767.81-1.28 1.584-1.476.452-.115.98-.086 1.445-.1l2.771-.056 3.94-.182c3.707-.201 7.44-.653 11.099-1.27 10.724-1.811 20.583-5.74 29.893-11.307l4.784-3C57.086 1.756 58.265.86 59.59.155Z"
              ></path><path
                fill="#2ED887"
                d="M59.332 15.465c.408.054.785.118 1.154.31.844.44 1.614 1.106 2.427 1.615l4.563 2.591c5.297 2.84 11.008 5.106 16.766 6.81a94.719 94.719 0 0 0 13.188 2.902l4.278.554c.714.076 1.536.043 2.223.217.394.1.754.357.993.685.162.223.262.487.295.76.126 1.034.021 2.21.021 3.26l-.005 7.11-.001 11.55-.12 8.56c-.176 3.372-.534 6.844-1.179 10.157-.475 2.439-1.147 4.78-1.868 7.152-5.14 16.933-15.876 32.222-30.595 42.132-3.334 2.245-6.666 4.266-10.299 5.998-.474.226-1.241.719-1.757.751-.643-.183-1.287-.555-1.895-.843-2.112-.997-4.254-2.118-6.235-3.352-3.715-2.313-7.285-4.823-10.593-7.697-12.032-10.448-21.16-25.008-24.76-40.582-.831-3.599-1.448-7.298-1.726-10.98l-.26-9.88.003-11.029.019-8.164c.005-1.325-.076-2.728.037-4.044.023-.272.098-.519.253-.745.52-.765 1.173-.762 2.014-.83 11.72-.954 23.029-4.422 33.493-9.748l4.982-2.726c1.482-.85 2.903-2.119 4.584-2.494Z"
              ></path><path
                fill="#fff"
                d="M30.416 63.636c.963-1.172 2.099-2.259 3.176-3.328.674-.667 1.35-1.43 2.132-1.965.712.502 1.31 1.225 1.92 1.848l2.7 2.685L52.39 74.759l3.655-3.495 7.376-7.313 11.737-11.665 4.465-4.466c.913-.93 1.827-2.067 2.88-2.825.742-.534 1.714-.771 2.62-.791a4.318 4.318 0 0 1 3.032 1.18c1.028.99 1.364 2.071 1.385 3.466-.089 1-.36 1.85-.992 2.645-.557.7-1.28 1.321-1.917 1.95l-3.1 3.112-11.72 11.791-11.756 11.855-4.01 4.084c-.642.652-1.28 1.405-1.986 1.98-.52.424-1.102.543-1.762.463-.599-.073-1.14-.445-1.58-.834-1.205-1.064-2.312-2.314-3.446-3.457l-7.83-7.937-6.045-6.016c-.999-1.003-2.203-2.064-3.024-3.21-.215-.3-.247-.655-.184-1.02.038-.218.135-.42.227-.62Z"
              ></path><path
                fill="#fff"
                d="m59.2 109.582-.007 14.281c-.902-.426-1.76-.948-2.638-1.42-6.12-3.291-11.935-7.67-16.839-12.6-11.734-11.797-19.602-27.328-21.194-43.98-.414-4.333-.295-8.712-.295-13.06l.003-18.172c2.701-.19 5.363-.64 8.018-1.16 8.373-1.64 16.73-4.571 24.374-8.361l5.383-2.88c1.085-.63 2.167-1.428 3.313-1.925l-.006 33.464-.002 6.87c-.001.476.122 1.371-.007 1.802-.01.033-.217.213-.245.24l-.914.898-3.551 3.462c-.659.655-1.35 1.504-2.104 2.038l-3.95-3.856-6.56-6.389-2.418-2.377c-.515-.506-1.02-1.074-1.626-1.47-.834-.547-1.744-.714-2.723-.645-.698.049-1.51.345-2.092.736-1.091.734-2.075 1.954-3.004 2.897-.41.415-.974.874-1.312 1.338l-.03.064c-1.312 1.194-2.464 2.27-2.733 4.099l-.023.147a5.867 5.867 0 0 0 1.162 4.403c1.141 1.474 2.717 2.778 4.041 4.096l8.322 8.282 6.903 6.959c.827.821 1.65 1.837 2.57 2.536.916.697 1.994.995 3.127 1.11 1.425.144 2.985-.276 4.076-1.206l2.988-2.975-.007 22.754Z"
              ></path></svg
            >
            <p class="hero__rfs-text h3">
              Verified by soccer associations, leagues and clubs
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section id="tool" class="section-space">
    <div class="container">
      <div class="tool">
        <div class="tool__headline-wrap">
          <h2 class="tool__headline">
            Offers a perfect tool for schools to earn extra income, motivate
            young footballers, and help them progress
          </h2>
        </div>
        <div class="tool__points">
          <h4 class="tool__point">
            Get the required number of tests or opt for an unlimited plan for
            your players
          </h4>
          <h4 class="tool__point">
            Add the license cost into your membership and earn
          </h4>
        </div>
        <RegisterButton id="tool-registration" class="tool__register-button">
          <slot slot="subtitle">Get 15 free tests</slot>
          <slot slot="label">Sign up your academy</slot>
        </RegisterButton>
      </div>
    </div>
  </section>
  <section id="benefits" class="section-space">
    <div class="container">
      <div class="benefits">
        <h2 class="benefits__headline">JuniCoach offers</h2>
        <div class="benefits__content">
          <div class="benefits__cards">
            <div class="benefits__card">
              <Picture
                class="benefits__players-cards is--mobile"
                src={PlayersCards}
                alt="Карточки игроков"
              />
              <svg
                class="benefits__card-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="129"
                height="128"
                fill="none"
                viewBox="0 0 129 128"
                ><path
                  fill="#FFE83F"
                  d="m20.56 15.07 43.823-9.738 43.823 9.739a5.333 5.333 0 0 1 4.177 5.206V73.54a31.998 31.998 0 0 1-14.25 26.625l-33.75 22.5-33.75-22.5a32 32 0 0 1-14.25-26.626V20.278c0-2.5 1.736-4.664 4.176-5.206Zm43.823 43.595c7.364 0 13.333-5.97 13.333-13.333 0-7.364-5.97-13.333-13.333-13.333-7.364 0-13.333 5.97-13.333 13.333 0 7.364 5.97 13.333 13.333 13.333ZM40.529 85.332h47.707c-1.326-12-11.5-21.333-23.853-21.333-12.354 0-22.527 9.333-23.854 21.333Z"
                ></path></svg
              >

              <p class="benefits__description h4">
                <b>For Players:</b> digital cards resembling real footballers like
                in FIFA, featuring precisely calculated performance attributes
              </p>
            </div>
            <div class="benefits__card">
              <svg
                class="benefits__card-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="129"
                height="128"
                fill="none"
                viewBox="0 0 129 128"
                ><path
                  fill="#FFE83F"
                  d="m85.716 10.666 26.667 26.667v74.71a5.294 5.294 0 0 1-5.298 5.29H21.681c-2.926 0-5.298-2.429-5.298-5.29V15.956a5.295 5.295 0 0 1 5.298-5.29h64.035ZM59.05 37.333v53.333h10.666V37.333H59.05Zm21.333 21.333v32h10.666v-32H80.383ZM37.716 69.333v21.333h10.667V69.333H37.716Z"
                ></path></svg
              >

              <p class="benefits__description h4">
                <b>For Parents:</b> development recommendations. <b>
                  For Coaches:</b
                > training recommendations
              </p>
            </div>
            <div class="benefits__card">
              <svg
                class="benefits__card-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="129"
                height="129"
                fill="none"
                viewBox="0 0 129 129"
                ><path
                  fill="#FFE83F"
                  d="M61.054 124V4h7.669v120h-7.669Zm18.471-79.219c-.312-3.437-1.699-6.11-4.162-8.015-2.431-1.938-5.907-2.907-10.428-2.907-2.993 0-5.487.391-7.482 1.172-1.995.782-3.491 1.86-4.489 3.235-.998 1.343-1.512 2.89-1.543 4.64-.063 1.438.218 2.703.842 3.797.654 1.094 1.59 2.063 2.805 2.906 1.247.813 2.744 1.532 4.49 2.157 1.745.625 3.71 1.172 5.892 1.64l8.23 1.875c4.738 1.032 8.916 2.407 12.532 4.125 3.648 1.719 6.703 3.766 9.166 6.14 2.494 2.376 4.38 5.11 5.658 8.204 1.278 3.094 1.933 6.563 1.964 10.406-.031 6.063-1.559 11.266-4.583 15.61-3.024 4.343-7.373 7.671-13.046 9.984-5.643 2.312-12.455 3.469-20.436 3.469-8.012 0-14.995-1.203-20.95-3.61-5.954-2.406-10.583-6.062-13.888-10.968-3.304-4.907-5.004-11.11-5.097-18.61h22.166c.187 3.094 1.013 5.672 2.478 7.735 1.465 2.062 3.476 3.625 6.032 4.687 2.588 1.063 5.58 1.594 8.979 1.594 3.117 0 5.767-.422 7.95-1.266 2.213-.844 3.912-2.015 5.096-3.515 1.185-1.5 1.793-3.22 1.824-5.157-.031-1.812-.592-3.359-1.683-4.64-1.091-1.313-2.775-2.438-5.05-3.375-2.245-.969-5.113-1.86-8.605-2.672L54.18 71.078c-8.293-1.906-14.824-4.984-19.594-9.234-4.77-4.282-7.139-10.063-7.108-17.344-.03-5.938 1.56-11.14 4.77-15.61 3.211-4.468 7.654-7.953 13.328-10.453 5.673-2.5 12.142-3.75 19.406-3.75 7.42 0 13.857 1.266 19.313 3.797 5.487 2.5 9.742 6.016 12.766 10.547 3.024 4.532 4.567 9.782 4.63 15.75H79.525Z"
                ></path></svg
              >
              <p class="benefits__description h4">
                <b>For Schools and Academies:</b> additional income opportunities
                and convenient testing tool
              </p>
            </div>
            <div class="benefits__card">
              <svg
                class="benefits__card-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="129"
                height="128"
                fill="none"
                viewBox="0 0 129 128"
                ><path
                  fill="#FFE83F"
                  d="m96.546 88.624 22.841 22.841-7.543 7.543-22.84-22.841a47.792 47.792 0 0 1-29.957 10.501c-26.496 0-48-21.504-48-48s21.504-48 48-48 48 21.504 48 48a47.791 47.791 0 0 1-10.501 29.956Zm-10.7-3.957c6.52-6.721 10.534-15.89 10.534-25.999 0-20.627-16.706-37.333-37.333-37.333S21.714 38.04 21.714 58.668 38.42 96.001 59.047 96.001c10.11 0 19.278-4.013 25.999-10.535l.8-.8ZM65.328 38.275A10.669 10.669 0 0 0 59.047 48c0 5.892 4.775 10.667 10.666 10.667a10.67 10.67 0 0 0 9.728-6.282c.61 1.986.94 4.095.94 6.282C80.381 70.449 70.829 80 59.047 80s-21.333-9.55-21.333-21.332 9.55-21.333 21.333-21.333c2.187 0 4.296.328 6.282.94Z"
                ></path></svg
              >
              <p class="benefits__description h4">
                Players with high test scores receive attention from clubs and
                are invited to trials
              </p>
              <p class="small-text">
                You can also choose to hide players from public viewing at your
                discretion
              </p>
            </div>

            <RegisterButton
              id="benefits-registration"
              class:list="benefits__button"
              arrow
            >
              <slot slot="subtitle">Get 15 free tests</slot>
              <slot slot="label">Sign up your academy</slot>
            </RegisterButton>
          </div>
          <Picture
            class="benefits__players-cards"
            src={PlayersCards}
            alt="Карточки игроков"
          />
        </div>
      </div>
    </div>
  </section>

  <section id="tests" class="section-space overflow-hidden">
    <div class="container">
      <div class="tests__head">
        <div class="tests__head-title-wrap">
          <p class="tests__top-description">
            For soccer players aged 8 to 21 years old
          </p>
          <h2 class="tests__headline">Tests & Analysis</h2>
          <p class="h3 tests__description">
            14 comprehensive tests designed to accurately assess over 70
            technical and physical attributes
          </p>
        </div>
        <div class="tests__navigation-wrap">
          <div class="tests__navigation">
            <button class="button tests__navigation-button is--prev"
              >Prev</button
            >
            <p class="tests__navigation-counter p2">0 / 00</p>
            <button class="button tests__navigation-button is--next"
              >Next</button
            >
          </div>
        </div>
      </div>
    </div>

    <div class="container is--tests swiper">
      <div class="tests is--preview swiper-wrapper">
        <!-- Tests -->
        <TestCard
          testName="15m Sprint"
          type="large"
          video={m15SprintMP4}
          categories={["pace", "agility"]}
          icon={Run15Icon}
          poster={optimizedPosters["Sprint15Poster"].src}
        />
        <!-- 3 cards -->
        <div class="swiper-slide tests__slide-group is--1">
          <TestCard
            testName="Coordination ladder left-right"
            type="small"
            video={ladderLRMP4}
            categories={["pace", "agility"]}
            icon={LadderLRIcon}
            poster={optimizedPosters["LadderLRPoster"].src}
          />
          <TestCard
            testName="Coordination ladder back-forth"
            type="small"
            video={ladderBFMP4}
            categories={["pace", "agility"]}
            icon={LadderFBIcon}
            poster={optimizedPosters["LadderBFPoster"].src}
          />
          <TestCard
            testName="High jump"
            type="small"
            video={highJumpMP4}
            categories={["physical", "agility"]}
            icon={jumpIcon}
            poster={optimizedPosters["JumpPoster"].src}
          />
        </div>
        <!-- 3 cards End-->

        <!-- 2 cards -->
        <div class="swiper-slide tests__slide-group is--2">
          <TestCard
            testName="Serpent"
            type="medium"
            video={serpentMP4}
            categories={["dribbling"]}
            icon={SerpentIcon}
            poster={optimizedPosters["SerpentPoster"].src}
          />
          <TestCard
            testName="Power kick. RF/LF"
            type="medium"
            video={powerKickMP4}
            categories={["shooting"]}
            icon={KickIcon}
            poster={optimizedPosters["KickPoster"].src}
          />
        </div>
        <!-- 2 cards End-->
        <TestCard
          testName="15m sprint from run up"
          type="large"
          video={m15SprintFromRunMP4}
          categories={["pace"]}
          icon={Run15upIcon}
          poster={optimizedPosters["Run15UpPoster"].src}
        />
        <TestCard
          testName="Arrow"
          type="large"
          video={arrowMP4}
          categories={["agility"]}
          icon={ArrowIcon}
          poster={optimizedPosters["ArrowPoster"].src}
        />
        <TestCard
          testName="Dribbling 15m with run up. RF/LF"
          type="large"
          video={dribblingMP4}
          categories={["dribbling"]}
          icon={Dribbling15Icon}
          poster={optimizedPosters["DribblingPoster"].src}
        />
        <TestCard
          testName="Arrow with the ball. RF/LF"
          type="large"
          video={arrowWithBallMP4}
          categories={["dribbling"]}
          icon={ArrowBallIcon}
          poster={optimizedPosters["ArrowBallPoster"].src}
        />
        <TestCard
          testName="Push ups"
          type="large"
          video={pushUpsMP4}
          categories={["physical"]}
          icon={PushUpsIcon}
          poster={optimizedPosters["PushUpsPoster"].src}
        />

        <!-- Tests End -->
      </div>
      <div class="tests__navigation-wrap is--mobile">
        <div class="tests__navigation">
          <button class="button tests__navigation-button is--prev">Prev</button>
          <p class="tests__navigation-counter p2">0 / 00</p>
          <button class="button tests__navigation-button is--next">Next</button>
        </div>
      </div>
      <div class="tests__pagination is--bullets"></div>
    </div>
  </section>
  <section id="price" class="section-space">
    <div class="container">
      <div class="price">
        <h2 class="price__headline">
          The most accessible<br />AI-testing protocol
        </h2>
        <RegisterButton id="price-registration" class="price__register-button">
          <slot slot="subtitle">Get 15 free tests</slot>
          <slot slot="label">Sign up your academy</slot>
        </RegisterButton>
      </div>
    </div>
  </section>

  <section class="section-space">
    <div class="container">
      <div class="help">
        <h2 class="help__headline">Guides and manuals</h2>
        <div class="price__grid">
          <a
            class="price__plan help__card"
            href="/hc/articles/setup-academy"
            target="_blank"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="112"
              height="112"
              fill="none"
              viewBox="0 0 112 112"
              ><path
                fill="var(--primary)"
                d="M104.099 46.906a3.503 3.503 0 0 0-1.702-2.363l-13.05-7.437-.053-14.71a3.5 3.5 0 0 0-1.239-2.66 48.962 48.962 0 0 0-16.065-9.042 3.5 3.5 0 0 0-2.826.258L56 18.31l-13.178-7.372a3.502 3.502 0 0 0-2.83-.263 48.967 48.967 0 0 0-16.048 9.078 3.5 3.5 0 0 0-1.238 2.656l-.066 14.722-13.05 7.437a3.501 3.501 0 0 0-1.702 2.363 46.58 46.58 0 0 0 0 18.182 3.5 3.5 0 0 0 1.702 2.363l13.05 7.437.053 14.713a3.5 3.5 0 0 0 1.238 2.66 48.97 48.97 0 0 0 16.065 9.044 3.51 3.51 0 0 0 2.826-.259L56 93.692l13.177 7.372c.522.29 1.11.441 1.707.437a3.52 3.52 0 0 0 1.124-.184 49.035 49.035 0 0 0 16.047-9.069 3.5 3.5 0 0 0 1.239-2.656l.065-14.721 13.051-7.438a3.51 3.51 0 0 0 1.702-2.362 46.603 46.603 0 0 0-.013-18.165ZM55.999 73.5a17.5 17.5 0 1 1 0-34.998A17.5 17.5 0 0 1 56 73.5Z"
              ></path></svg
            >
            <h4 class="price__plan-title">Setup your academy</h4>
          </a>
          <a
            class="price__plan help__card"
            href="/hc/articles/become-representative"
            target="_blank"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="112"
              height="112"
              fill="none"
              viewBox="0 0 112 112"
              ><path
                fill="var(--primary)"
                d="M56 33.25a19.25 19.25 0 1 1 0 38.499 19.25 19.25 0 0 1 0-38.499ZM77 28h7v7a3.5 3.5 0 1 0 7 0v-7h7a3.499 3.499 0 1 0 0-7h-7v-7a3.5 3.5 0 0 0-7 0v7h-7a3.5 3.5 0 1 0 0 7Zm19.841 17.548a3.5 3.5 0 0 0-2.874 4.03c.355 2.122.534 4.27.533 6.422a38.334 38.334 0 0 1-9.73 25.554 34.94 34.94 0 0 0-9.52-9.323 1.75 1.75 0 0 0-2.117.14 26.167 26.167 0 0 1-34.243 0 1.75 1.75 0 0 0-2.14-.14 34.875 34.875 0 0 0-9.533 9.323A38.5 38.5 0 0 1 56 17.5c2.152 0 4.3.178 6.423.534a3.5 3.5 0 0 0 1.155-6.904 45.465 45.465 0 1 0 37.292 37.293 3.497 3.497 0 0 0-4.029-2.875Z"
              ></path></svg
            >
            <h4 class="price__plan-title">Connect representative</h4>
          </a>
        </div>
      </div>
    </div>
  </section>
</Layout>

<footer>JuniStat Corp © Delaware, USA</footer>

<script>
  const matchMobile = window.matchMedia(
    "screen and (max-width: 992px)",
  ).matches;

  //#region [Slider]
  import { Swiper } from "swiper";
  import { Navigation, Pagination } from "swiper/modules";
  // import Swiper and modules styles

  const testsSlider = new Swiper(".container.swiper", {
    modules: [Navigation, Pagination],
    slidesPerView: "auto",
    slideToClickedSlide: true,
    autoHeight: true,
    init: false,
    spaceBetween: 0,

    breakpoints: {
      320: {
        spaceBetween: 16,
      },

      992: {
        spaceBetween: 0,
      },
    },

    // If we need pagination
    pagination: {
      el: ".tests__pagination.is--bullets",
      type: "bullets",
      clickable: true,
    },

    on: {
      activeIndexChange: countTestsSlides,
      init: countTestsSlides,
      slideChange: () => {
        prevSlideButton.forEach((button) => {
          checkNavButton(button, "prev");
        });
        nextSlideButton.forEach((button) => {
          checkNavButton(button, "next");
        });
      },
    },
  });

  const nextSlideButton = document.querySelectorAll(
    ".tests__navigation-button.is--next",
  ) as NodeListOf<HTMLElement>;
  const prevSlideButton = document.querySelectorAll(
    ".tests__navigation-button.is--prev",
  ) as NodeListOf<HTMLElement>;

  function checkNavButton(button: HTMLElement, direction: "prev" | "next") {
    switch (true) {
      case testsSlider.isEnd:
        button.classList.add("swiper-button-disabled");
        button.setAttribute("disabled", "true");

        prevSlideButton.forEach((button) => {
          button.classList.remove("swiper-button-disabled");
          button.removeAttribute("disabled");
        });
        break;
      case testsSlider.isBeginning:
        button.classList.add("swiper-button-disabled");
        button.setAttribute("disabled", "true");

        nextSlideButton.forEach((button) => {
          button.classList.remove("swiper-button-disabled");
          button.removeAttribute("disabled");
        });
        break;
      default:
        prevSlideButton.forEach((button) => {
          button.classList.remove("swiper-button-disabled");
          button.removeAttribute("disabled");
        });
        nextSlideButton.forEach((button) => {
          button.classList.remove("swiper-button-disabled");
          button.removeAttribute("disabled");
        });
    }
  }

  nextSlideButton.forEach((button) => {
    button.addEventListener("click", () => {
      testsSlider.slideNext();
      checkNavButton(button, "next");
    });
  });

  prevSlideButton.forEach((button) => {
    if (testsSlider.isBeginning) {
      button.classList.add("swiper-button-disabled");
      button.setAttribute("disabled", "true");
    }

    button.addEventListener("click", () => {
      testsSlider.slidePrev();
      checkNavButton(button, "prev");
    });
  });

  function countTestsSlides() {
    const counters = document.querySelectorAll(
      ".tests__navigation-counter",
    ) as NodeListOf<HTMLElement>;
    counters.forEach((counter) => {
      counter.innerText = `${testsSlider.activeIndex + 1} / ${
        testsSlider.slides.length
      }`;
    });
  }

  if (matchMobile) {
    const slideGroup = document.querySelectorAll(
      ".swiper-slide.tests__slide-group",
    ) as NodeListOf<HTMLElement>;

    slideGroup.forEach((group) => {
      const slides = group.querySelectorAll(".swiper-slide");
      slides.forEach((slide) => {
        group.insertAdjacentElement("beforebegin", slide);
      });
      group.remove();
    });
  }

  testsSlider.init();
  //#endregion [Slider]

  //#region [Animation]
  import gsap from "gsap";
  import ScrollTrigger from "gsap/ScrollTrigger";
  gsap.registerPlugin(ScrollTrigger);

  //#region [Hero]

  let mm = gsap.matchMedia();

  mm.add("(max-width: 460px)", () => {
    gsap
      .timeline({
        scrollTrigger: {
          trigger: "#hero",
          start: "50% 30%",
          end: "150% bottom",
          scrub: 1,
        },
      })
      .to(".hero__image", {
        x: "-30%",
      });
  });

  //#endregion [Hero]

  //#region [Tool]
  gsap
    .timeline({
      scrollTrigger: {
        trigger: ".tool__headline-wrap",
        start: "top 80%",
        end: "bottom bottom",
        toggleActions: "play none play play", // onEnter, onLeave, onEnterBack, and onLeaveBack
      },
    })
    .from(".tool__headline-icon-wrap", {
      scale: 0,
      stagger: 0.2,
    });
  //#endregion [Tool]

  //#region [Benefits]
  document.querySelectorAll(".benefits__card").forEach((card) => {
    gsap
      .timeline({
        scrollTrigger: {
          trigger: card,
          start: "top 80%",
          end: "bottom bottom",
        },
      })
      .from(card, { autoAlpha: 0, y: "10%", duration: 0.4 });
  });

  //#endregion [Benefits]

  //#region [Price]
  document.querySelectorAll(".price__plan").forEach((card) => {
    gsap
      .timeline({
        scrollTrigger: {
          trigger: card,
          start: "top 80%",
          end: "bottom bottom",
        },
      })
      .from(card, { autoAlpha: 0, y: "10%" });
  });
  //#endregion [Price]
  //#endregion [Animation]

  //#region [Analytics]
  import { track } from "@amplitude/analytics-browser";

  document
    .querySelectorAll(
      "#tool-registration, #benefits-registration, #price-registration",
    )
    .forEach((button) => {
      button.addEventListener("click", (e) => {
        track("Click registration JuniCoach page", {
          // @ts-ignore
          place: e.currentTarget.id,
        });
      });
    });

  //#endregion [Analytics]
</script>

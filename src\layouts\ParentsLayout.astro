---
//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

//#endregion [i18n]

//#region [Styles]
import "@styles/pages/parents.scss";
//#endregion [Styles]

//#region [Built-in components]
import { Image } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
import DownloadButtons from "@components/DownloadButtons.astro";
//#endregion [Components]

//#region [Icons]
//#endregion [Icons]

//#region [Images]
import LavricImg from "@assets/images/clubs/vasile-lavric_psg-min.jpg";

import playerCard1 from "@assets/images/parents/about/player_card-01-min.png";
import playerCard2 from "@assets/images/parents/about/player_card-02-min.png";
import playerCard3 from "@assets/images/parents/about/player_card-03-min.png";

import skillCardAgility from "@assets/images/parents/about/agility-skill.jpg";
import skillCardPace from "@assets/images/parents/about/pace-skill.jpg";
import skillCardDribbling from "@assets/images/parents/about/dribbling-skill.jpg";

import ChartImg from "@assets/images/parents/chart/chart.webp";
//#endregion [Images]

//#region [Videos]
import parensVideoWebM from "@assets/videos/parents/parents.webm";
import parensVideoMp4 from "@assets/videos/parents/parents.mp4";
//#endregion [Videos]
---

<Layout>
  <!-- Hero -->
  <section id="hero">
    <div class="container">
      <div class="hero">
        <div class="hero__top">
          <h1
            class="hero__title"
            set:html={t("parents.title", {
              copyright: `<sup style="font-size: 2.4rem">®</sup>`,
            })}
          />

          <div class="hero__quote-block">
            <q class="hero__quote-text"><q class="h3">{t("clubs.quote")}</q></q>
            <div class="hero__quote-author">
              <Image
                class="hero__author-avatar hero__author-avatar-img"
                alt="Vasile Lavrice, PSG"
                src={LavricImg}
              />
              <p>Vasile Lavrice PSG,<br />U7-11</p>
            </div>
          </div>
          <DownloadButtons app="player" place="Hero" />
        </div>
        <div class="hero__video">
          <svg
            class="video__play-button"
            xmlns="http://www.w3.org/2000/svg"
            width="80"
            height="80"
            fill="none"
            viewBox="0 0 80 80"
            ><path
              fill="#fff"
              d="M40 5C20.672 5 5 20.672 5 40s15.672 35 35 35 35-15.672 35-35S59.328 5 40 5Zm0 64.063c-16.047 0-29.063-13.016-29.063-29.063 0-16.047 13.016-29.063 29.063-29.063 16.047 0 29.063 13.016 29.063 29.063 0 16.047-13.016 29.063-29.063 29.063Z"
            ></path><path
              fill="#fff"
              d="M50.744 39.494 33.975 27.12a.609.609 0 0 0-.885.18.63.63 0 0 0-.09.326v24.751a.633.633 0 0 0 .335.555.608.608 0 0 0 .64-.048L50.744 40.5a.613.613 0 0 0 .256-.502.623.623 0 0 0-.256-.503Z"
            ></path></svg
          >
          <video
            class="hero__video-tag"
            autoplay
            loop
            style="width: 110%; margin-bottom: -1rem"
            muted
            playsinline
          >
            <source src={parensVideoWebM} />
            <source src={parensVideoMp4} />
          </video>
        </div>
      </div>
    </div>
  </section>
  <!-- About -->
  <section id="about" class="section-space">
    <div class="container">
      <div class="about">
        <div class="about__profiles about__grid">
          <div class="about__profiles-cards">
            <Image
              class="about__profiles-card"
              src={playerCard1}
              alt="Player's card"
              width="768"
              quality="mid"
            />
            <Image
              class="about__profiles-card"
              src={playerCard2}
              alt="Player's card"
              width="768"
              quality="mid"
            />
            <Image
              class="about__profiles-card"
              src={playerCard3}
              alt="Player's card"
              width="768"
              quality="mid"
            />
          </div>
          <div class="about__profiles-info">
            <h2 class="about__profiles-info-title">
              {t("parents.about_cards")}
            </h2>
          </div>
        </div>
        <div class="about__skills about__grid">
          <div class="about__skills-info">
            <h2 class="about__skills-info-title">
              {t("parents.about_skills_title")}
            </h2>
            <p class="about__skills-info-text">
              {t("parents.about_skills_subtitle")}
            </p>
          </div>
          <div class="about__skills-cards">
            <Image
              class="about__skills-card"
              src={skillCardAgility}
              alt="Skills card"
              quality="mid"
            />

            <Image
              class="about__skills-card"
              src={skillCardDribbling}
              alt="Skills card"
              quality="mid"
            />

            <Image
              class="about__skills-card"
              src={skillCardPace}
              alt="Skills card"
              quality="mid"
            />
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- Chart -->
  <section id="chart" class="section-space">
    <div class="container">
      <div class="chart">
        <h2 class="chart__title h3">
          {t("parents.chart_text")}
        </h2>
        <Image
          src={ChartImg}
          alt="Example of results in graph view"
          class="chart__graph"
        />
      </div>
    </div>
  </section>
  <!-- Footer cards -->
  <section id="footer-cards" class="section-space">
    <div class="container">
      <div class="footer-cards">
        <!-- Create representative -->
        <div class="footer-cards__card is--accent">
          <DownloadButtons app="player" class="is--footer" place="Footer" />
          <h3 class="footer-cards__card-title">
            {t("parents.app_for_player")}
          </h3>
        </div>
        <a
          class="footer-cards__card"
          id="parent-profile-card"
          href="https://app.junistat.com/sign-up?role=mentor"
          target="_blank"
        >
          <svg
            class="footer-cards__card-icon"
            xmlns="http://www.w3.org/2000/svg"
            width="72"
            height="72"
            fill="none"
            viewBox="0 0 72 72"
          >
            <path
              fill="#000"
              d="M28.806 11.1c-3.462 5.811-1.905 15.681 4.272 27.075 2.79 5.136 2.091 9.06 1.014 11.445-2.946 6.534-11.025 8.397-19.575 10.368C8.625 61.35 9 62.598 9 72H3.015L3 68.277c0-7.56.597-11.925 9.534-13.989 10.095-2.331 20.064-4.419 15.27-13.254C13.605 14.847 23.754 0 39 0c9.963 0 17.91 6.351 17.91 18.501C56.91 29.166 51.063 39 49.761 42h-6.345c1.176-4.608 7.497-13.098 7.497-23.526 0-15.459-17.601-14.955-22.107-7.374zM69 57h-9v-9h-6v9h-9v6h9v9h6v-9h9v-6z"
            ></path>
          </svg>

          <h3 class="footer-cards__card-title">
            {t("parents.create_parent_profile")}
          </h3>
        </a>
      </div>
    </div>
  </section>
</Layout>

<script>
  import { track } from "@amplitude/analytics-browser";

  import gsap from "gsap";
  import ScrollTrigger from "gsap/ScrollTrigger";
  import debounce from "@src/utils/debounce";
  gsap.registerPlugin(ScrollTrigger);

  let isMobile = window.innerWidth >= 688 ? false : true;

  window.addEventListener("load", () => {
    function getScreenSize() {
      const state = window.innerWidth >= 688 ? false : true;
      isMobile = state;
      return state;
    }

    let animCtx = gsap.context(() => {
      const aboutCardsTl = gsap
        .timeline({
          scrollTrigger: {
            trigger: ".about__profiles-cards",
            start: "top 80%",
            end: "bottom bottom",
            scrub: true,
          },
        })
        .from(
          ".about__profiles-card",
          {
            scale: 0.4,
            rotateZ: 0,
            stagger: 0.3,
            duration: 2,
          },
          "showCards",
        );
      if (!isMobile) {
        aboutCardsTl.from(
          ".about__profiles-info",
          { y: "100%", duration: 2 },
          "showCards",
        );
      }

      const aboutSkillsTl = gsap
        .timeline({
          scrollTrigger: {
            trigger: ".about__skills-cards",
            start: "top 80%",
            end: "bottom bottom",
            scrub: true,
          },
        })
        .from(
          ".about__skills-card",
          {
            scale: 0.1,
            rotateZ: 0,
            stagger: 0.3,
            duration: 2,
          },
          "same",
        );

      if (!isMobile) {
        aboutSkillsTl.from(
          ".about__skills-info",
          { y: "100%", duration: 2 },
          "same",
        );
      }

      // Chart
      gsap
        .timeline({
          scrollTrigger: {
            trigger: ".chart",
            start: "top 50%",
            end: "bottom bottom",
            scrub: true,
          },
        })
        .from(".chart > *", { opacity: 0, y: "10%", stagger: 0.2 });

      if (isMobile) {
        gsap.to(".chart__graph", {
          x: "-50%",
          duration: 2,
          scrollTrigger: {
            trigger: ".chart",
            start: "top 20%",
            end: "bottom 40%",
            scrub: true,
          },
        });
      }
    });

    const updateScreenSize = debounce(getScreenSize, 400);
    window.addEventListener("resize", updateScreenSize);

    //#region [Track create parent profile]
    document
      .querySelector("#parent-profile-card")
      ?.addEventListener("click", () => {
        track("Click create parent profile");
      });
    //#endregion [Track create parent profile]
  });
</script>

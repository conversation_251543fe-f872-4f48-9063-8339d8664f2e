---
//#region [i18n]
import {
  getLangFromUrl,
  useTranslations,
  useTranslatedPath,
} from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);
//#endregion [i18n]

//#region [Built-in components]
import { Image, getImage } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
import PlayersGridStatic from "@components/players-grid/PlayersGridStatic.svelte";
import DownloadButtons from "@components/DownloadButtons.astro";
import AppButton from "@components/AppButton.astro";

//#endregion [Components]

//#region [Styles]
import "@styles/pages/rating.css";
//#endregion [Styles]

//#region [Icons]
//#endregion [Icons]

//#region [Images]
import CardRuImg from "@assets/images/example-cards/v2/card-scheme-ru-v2.png";
import CardEnImg from "@assets/images/example-cards/v2/card-scheme-en-v2.png";
import CardEsImg from "@assets/images/example-cards/v2/card-scheme-es-v2.png";

const currentCard = (): ImageMetadata => {
  switch (lang) {
    case "ru":
      return CardRuImg;
    case "es":
      return CardEsImg;
    default:
      return CardEnImg;
  }
};

import qrCode from "@assets/images/rating/qr-code.svg";

import ctaBackgroundImg from "@assets/images/rating/cta-bg-min.jpg";

const ctaBackgroundImgOptimized = await getImage({
  src: ctaBackgroundImg,
  format: "webp",
});
//#endregion [Images]
---

<Layout class:list={["rating"]}>
  <section id="example">
    <div class="container">
      <div class="example">
        <div class="example__info">
          <h1 class="example__info-title">{t("rating.example.title")}</h1>
          <ul class="example__info-list">
            <li class="example__info-list-item">
              <div>{t("rating.example.point1")}</div>
            </li>
            <li class="example__info-list-item">
              <div>{t("rating.example.point2")}</div>
            </li>
            <li class="example__info-list-item">
              <div>{t("rating.example.point3")}</div>
            </li>
            <li class="example__info-list-item">
              <div>{t("rating.example.point4")}</div>
            </li>
          </ul>
          <div class="example__download">
            <Image class="example__qr-code" src={qrCode} alt="QR code" />
            <DownloadButtons app="player" place="example" />
          </div>
        </div>
        <div class="example__card">
          <Image
            src={currentCard()}
            alt="Card example"
            loading="eager"
            class="example__card-img"
            fetchpriority="high"
            widths={[480, 780, 680, currentCard().width]}
            sizes={`(max-width: 530px) 480px, (max-width: 991px) 780px, (min-width: 992px) 680px, ${currentCard().width}px`}
          />
        </div>
      </div>
    </div>
  </section>
  <section class="section-space">
    <div class="container">
      <div class="rating">
        <PlayersGridStatic client:only="svelte" />
      </div>
    </div>
  </section>
  <!-- Scout -->
  <section id="scout" class="section-space">
    <div class="container">
      <div class="scout">
        <div class="scout__info">
          <h2>{t("rating.scout.title")}</h2>
          <div>{t("rating.scout.text")}</div>
        </div>
        <form class="scout__form" id="scout-form">
          <div class="scount__inputs-wrap">
            <input
              class="scout__form-input"
              type="email"
              name="email"
              id="scout-email"
              placeholder={"✉️ Email"}
              required
            />
            <AppButton
              as="button"
              id="scout-btn"
              style={"mod2"}
              form="scout-form"
            >
              {t("rating.scout.button") || "Subscribe"}
            </AppButton>
          </div>
          <div class="scout__form-terms" set:html={t("rating.scout.terms")} />
        </form>
        <!-- Form Success -->
        <div class="scout__form-result is--success" id="scout__form-success">
          <div id="scout__form-success-text">
            {t("rating.scout.form-success") || "Success"}
          </div>
        </div>
        <!-- Form Error -->
        <div class="scout__form-result is--error" id="scout__form-error">
          <div id="scout__form-error-text">
            {t("rating.scout.form-error") || "Error"}
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA -->
  <section id="cta" class="section-space">
    <div class="container">
      <div
        class="cta"
        style={`background-image: url(${ctaBackgroundImgOptimized.src})`}
      >
        <h2 class="cta__title">{t("rating.banner.title")}</h2>
        <h3 class="cta__subtitle">{t("rating.banner.subtitle")}</h3>
        <DownloadButtons app="player" place="CTA" />
      </div>
    </div>
  </section>
  <!-- Footer cards -->
  <section id="footer-cards" class="section-space-small">
    <div class="container">
      <div class="footer-cards">
        <a
          class="footer-cards__card"
          id="go-to-parents-card"
          href={translatePath("/parents")}
        >
          <svg
            class="footer-cards__card-icon"
            xmlns="http://www.w3.org/2000/svg"
            width="72"
            height="72"
            fill="none"
            viewBox="0 0 72 72"
            ><path
              fill="#000"
              d="M28.806 11.1c-3.462 5.811-1.905 15.681 4.272 27.075 2.79 5.136 2.091 9.06 1.014 11.445-2.946 6.534-11.025 8.397-19.575 10.368C8.625 61.35 9 62.598 9 72H3.015L3 68.277c0-7.56.597-11.925 9.534-13.989 10.095-2.331 20.064-4.419 15.27-13.254C13.605 14.847 23.754 0 39 0c9.963 0 17.91 6.351 17.91 18.501C56.91 29.166 51.063 39 49.761 42h-6.345c1.176-4.608 7.497-13.098 7.497-23.526 0-15.459-17.601-14.955-22.107-7.374zM69 57h-9v-9h-6v9h-9v6h9v9h6v-9h9v-6z"
            ></path>
          </svg>
          <h3 class="footer-cards__card-title">
            {t("rating.card_parents")}
          </h3>
        </a>

        <a
          class="footer-cards__card"
          id="go-to-clubs-card"
          href={translatePath("/clubs")}
        >
          <svg
            class="footer-cards__card-icon"
            xmlns="http://www.w3.org/2000/svg"
            width="72"
            height="72"
            fill="none"
            viewBox="0 0 72 72"
            ><path
              fill="#000"
              fill-rule="evenodd"
              d="M21 6c5.085 5.826 7.113 9 12 9h39v51H0V6h21zm18 33v-9h-6v9h-9v6h9v9h6v-9h9v-6h-9z"
              clip-rule="evenodd"></path>
          </svg>
          <h3 class="footer-cards__card-title">
            {t("rating.card_clubs")}
          </h3>
        </a>
      </div>
    </div>
  </section>
</Layout>

<script>
  import { track } from "@amplitude/analytics-browser";

  window.addEventListener("load", () => {
    //#region [Track footer cards]
    document
      .querySelector("#go-to-parents-card")
      ?.addEventListener("click", () => {
        track("Click Parens card in footer");
      });

    document
      .querySelector("#go-to-clubs-card")
      ?.addEventListener("click", () => {
        track("Click Clubs card in footer");
      });
    //#endregion [Track footer cards]
  });

  //#region [Form]
  const form = document.querySelector(".scout__form") as HTMLFormElement;

  form.addEventListener("submit", async (e) => {
    e.preventDefault();
    const formData = new FormData(form);

    const userEmail = formData.get("email");
    const successMessage = document.querySelector(
      "#scout__form-success",
    ) as HTMLElement;
    const errorMessage = document.querySelector(
      "#scout__form-error",
    ) as HTMLElement;

    track("Scout form submit", {
      email: userEmail,
    });

    try {
      const sendData = await fetch(
        "https://n8n.kobro.ru/webhook/junistat-scout-newsletter",
        {
          headers: {
            "Content-Type": "application/json",
          },
          method: "POST",
          body: JSON.stringify({
            email: userEmail,
            lang: document.documentElement.getAttribute("lang") || "",
          }),
        },
      );

      successMessage.style.display = "block";
      errorMessage.style.display = "none";
      form.style.display = "none";
    } catch (error) {
      errorMessage.style.display = "block";
    }
  });
  //#endregion [Form]
</script>

---
//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

//#endregion [i18n]

//#region [Styles]
import "swiper/css";
import "@styles/pages/scouting.scss";
//#endregion [Styles]

//#region [Built-in components]
import { Image } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
import AppButton from "@components/AppButton.astro";
//#endregion [Components]

//#region [Icons]
//#endregion [Icons]

//#region [Images]
const AfricanPlayersImg = await Astro.glob(
  "/src/assets/images/scouting/africans-cards/**/*.{png,svg,jpg}",
).then((files) => {
  return files.map((file) => {
    return file.default;
  });
});

const SkillCardsImages = await Astro.glob(
  "/src/assets/images/scouting/skill-cards/**/*.{png,svg,jpg}",
).then((files) => {
  return files.map((file) => {
    return file.default;
  });
});

import MetricsImg from "@assets/images/scouting/metrics-cards.jpg";
import TrackPlayersImg from "@assets/images/scouting/track-players.jpg";

//#endregion [Images]

//#region [Videos]
import LadderVideoMp4 from "@assets/videos/scouting/ladder-cut-transcode.mp4";
import LadderVideoWebM from "@assets/videos/scouting/ladder-cut-transcode.webm";
//#endregion [Videos]
---

<Layout>
  <!-- Header -->
  <section id="hero">
    <div class="container">
      <div class="hero">
        <h1 class="hero__title">
          Get free access <br />to all open player profiles
        </h1>
        <h3 class="hero__subtitle">
          Get your immediate free access to our digital player database which
          can be sorted by region, age, skills etc.
        </h3>
        <AppButton
          as="a"
          class="hero__button"
          style="mod2"
          target="_blank"
          rel="nofollow"
          href="https://docs.google.com/forms/d/e/1FAIpQLSdVYm_x4lCSOscwdTUgbv4CRATcLPXlSIg6aMgjv4rK4z_mdw/viewform"
        >
          <h3>Get access</h3>
        </AppButton>
        <div class="hero__cards">
          <Image
            class="hero__card"
            src={AfricanPlayersImg[0]}
            width="560"
            alt="African player's card example"
          />
          <Image
            class="hero__card"
            src={AfricanPlayersImg[2]}
            alt="African player's card example"
          />
          <Image
            class="hero__card"
            src={AfricanPlayersImg[1]}
            alt="African player's card example"
          />
          <Image
            class="hero__card"
            src={AfricanPlayersImg[3]}
            alt="African player's card example"
          />
        </div>
      </div>
    </div>
  </section>

  <!-- Player example -->
  <section id="player-example" class="section-space">
    <div class="container">
      <div class="player-example">
        <h2 class="player-example__title">
          {t("clubs.player_example_title")}
        </h2>
        <div class="player-example__profile-wrap">
          <a
            class="player-example__card-wrap"
            href="https://app.junistat.com/player/f587803e-64ce-452c-9029-deb80c25d554/training"
            target="_blank"
          >
            <Image
              class="player-example__card-img"
              src={AfricanPlayersImg[0]}
              width="590"
              alt="Player personal card"
            />
            <AppButton
              as="div"
              aria-label="Check profile"
              class="player-example__card-button button--primary"
            >
              {t("clubs.player_open_profile")}
            </AppButton>
          </a>
          <!-- Slider -->
          <div class="swiper player-example__skills-slider">
            <div class="swiper-wrapper player-example__skills-grid">
              <div class="swiper-slide player-example__skills-slide">
                <Image
                  class="player-example__skills-img"
                  src={SkillCardsImages[0]}
                  alt="PLayer's skill graphs"
                />
              </div>
              <div class="swiper-slide player-example__skills-slide">
                <Image
                  class="player-example__skills-img"
                  src={SkillCardsImages[1]}
                  alt="PLayer's skill graphs"
                />
              </div>
              <div class="swiper-slide player-example__skills-slide">
                <Image
                  class="player-example__skills-img"
                  src={SkillCardsImages[2]}
                  alt="PLayer's skill graphs"
                />
              </div>
              <div class="swiper-slide player-example__skills-slide">
                <Image
                  class="player-example__skills-img"
                  src={SkillCardsImages[3]}
                  alt="PLayer's skill graphs"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Metrics -->
  <section id="metrics" class="section-space">
    <div class="container">
      <div class="metrics">
        <h2 class="metrics__title">
          Computer vision processed player metrics linked to video
        </h2>
        <div class="metrics__content">
          <Image
            class="metrics__image-wrap"
            alt="Metrics cards"
            src={MetricsImg}
          />
          <div class="metrics__video-wrap video">
            <svg
              class="video__play-button"
              xmlns="http://www.w3.org/2000/svg"
              width="80"
              height="80"
              fill="none"
              viewBox="0 0 80 80"
              ><path
                fill="#fff"
                d="M40 5C20.672 5 5 20.672 5 40s15.672 35 35 35 35-15.672 35-35S59.328 5 40 5Zm0 64.063c-16.047 0-29.063-13.016-29.063-29.063 0-16.047 13.016-29.063 29.063-29.063 16.047 0 29.063 13.016 29.063 29.063 0 16.047-13.016 29.063-29.063 29.063Z"
              ></path><path
                fill="#fff"
                d="M50.744 39.494 33.975 27.12a.609.609 0 0 0-.885.18.63.63 0 0 0-.09.326v24.751a.633.633 0 0 0 .335.555.608.608 0 0 0 .64-.048L50.744 40.5a.613.613 0 0 0 .256-.502.623.623 0 0 0-.256-.503Z"
              ></path></svg
            >
            <video class="metrics__video" autoplay loop muted playsinline>
              <source src={LadderVideoWebM} />
              <source src={LadderVideoMp4} />
            </video>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Track -->
  <section id="track" class="section-space">
    <div class="container">
      <div class="track">
        <div class="track__header">
          <h2 class="track__title">Track your academy players</h2>
          <h3 class="track__subtitle">
            Easy to keep an eye on your own players
          </h3>
          <div class="track__header-cols">
            <div class="tack__col">
              Get continuous data metrics of your teams with just 10 minutes
              testing per week and compare your players with talent from all
              over the world
            </div>
            <div class="tack__col">
              Store in one platform the player performance data from all of your
              academy’s branches
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="track__compare-wrap">
      <Image
        class="track__compare-image"
        src={TrackPlayersImg}
        alt="Compare players dashboard"
      />
    </div>
  </section>

  <!-- Start -->
  <section id="start" class="section-space">
    <div class="container">
      <div class="start">
        <div class="start__header">
          <div class="start__col">
            <h2 class="start__title">Fill in your contact details</h2>
            <h3 class="start__subtitle">
              After a free trial period, you can get access and track players’
              profiles worldwide
            </h3>
          </div>
          <div class="start__col start__price-col">
            <h3 class="start__price-title">Price per player</h3>
            <div class="start__price-wrap">
              <div class="start__price-block">
                <p>5 EUR</p>
                <p class="start__price-description p2">per month</p>
              </div>
              <div class="start__price-block">
                <p>or 50 EUR</p>
                <p class="start__price-description p2">per year</p>
              </div>
            </div>
          </div>
        </div>
        <div class="start__steps">
          <div class="start__step">
            <div class="start__step-circe">1</div>
            <p class="start__step-text">
              Leave your contact details in our form
            </p>
          </div>
          <div class="start__step">
            <div class="start__step-circe">2</div>
            <p class="start__step-text">Our team will get in touch with you</p>
          </div>
          <div class="start__step">
            <div class="start__step-circe">3</div>
            <p class="start__step-text">
              Start using the advantages of digital scouting. Get 1000+ players'
              profiles from academies all over the globe without leaving your
              office!
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer cards -->
  <section id="footer-cards" class="section-space">
    <div class="container">
      <div class="footer-cards">
        <a
          class="footer-cards__card is--accent"
          href="https://forms.gle/rbZPVMu3mkb8poMBA"
        >
          <h3 class="footer-cards__card-title">Book a demo ↗</h3>
        </a>
        <div class="footer-cards__card">
          <div class="footer-cards__social-links">
            <a
              class="footer-cards__social-link"
              href="tel:+56976335694"
              target="_blank"
              aria-label="Phone call"
            >
              <svg
                class="footer-cards__social-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="53"
                height="53"
                fill="none"
                viewBox="0 0 53 53"
                ><path
                  fill="#2C6269"
                  d="M26.384 52.765c14.568 0 26.38-11.813 26.38-26.384C52.765 11.814 40.953 0 26.382 0 11.814 0 0 11.812 0 26.384c0 14.568 11.812 26.38 26.384 26.38Z"
                ></path><path
                  fill="#fff"
                  d="M21.3 25.77a12.815 12.815 0 0 0 5.978 5.962 1.226 1.226 0 0 0 1.21-.091l3.834-2.56a1.212 1.212 0 0 1 1.164-.108l7.175 3.08a1.211 1.211 0 0 1 .735 1.274A7.358 7.358 0 0 1 34.1 39.75 20.848 20.848 0 0 1 13.25 18.9a7.36 7.36 0 0 1 6.423-7.297 1.21 1.21 0 0 1 1.272.735l3.082 7.19a1.227 1.227 0 0 1-.093 1.15l-2.558 3.893a1.227 1.227 0 0 0-.077 1.198Z"
                ></path></svg
              >
            </a>
            <a
              class="footer-cards__social-link"
              href="https://t.me/glebs1981"
              target="_blank"
              aria-label="Telegram"
            >
              <svg
                class="footer-cards__social-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="53"
                height="53"
                fill="none"
                viewBox="0 0 53 53"
                ><path
                  fill="#2CA4E0"
                  d="M26.383 52.765c14.57 0 26.382-11.812 26.382-26.383C52.765 11.813 40.953 0 26.382 0 11.813 0 0 11.812 0 26.383c0 14.57 11.812 26.382 26.383 26.382Z"
                ></path><path
                  fill="#fff"
                  fill-rule="evenodd"
                  d="M11.942 26.105c7.69-3.35 12.82-5.56 15.386-6.627 7.327-3.048 8.849-3.577 9.84-3.595.22-.003.708.05 1.024.307.267.217.34.51.375.715.035.205.079.673.044 1.038-.397 4.172-2.115 14.295-2.99 18.968-.37 1.977-1.097 2.64-1.802 2.705-1.532.14-2.695-1.013-4.18-1.986-2.321-1.522-3.633-2.47-5.887-3.954-2.605-1.717-.916-2.66.568-4.202.389-.404 7.14-6.544 7.27-7.1.016-.07.032-.33-.123-.467-.154-.137-.382-.09-.546-.053-.233.053-3.942 2.505-11.128 7.355-1.053.723-2.007 1.076-2.861 1.057-.942-.02-2.754-.532-4.101-.97-1.653-.537-2.966-.821-2.851-1.733.06-.476.713-.961 1.962-1.458Z"
                  clip-rule="evenodd"></path></svg
              >
            </a>
            <a
              class="footer-cards__social-link"
              href="https://wa.me/56948541496"
              aria-label="WhatsApp"
              target="_blank"
            >
              <svg
                class="footer-cards__social-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="53"
                height="53"
                fill="none"
                viewBox="0 0 53 53"
                ><path
                  fill="#42E760"
                  d="M0 25.327C0 13.387 0 7.418 3.71 3.71 7.417 0 13.387 0 25.326 0h2.11c11.94 0 17.91 0 21.619 3.71 3.709 3.708 3.709 9.678 3.709 21.617v2.11c0 11.94 0 17.91-3.71 21.619-3.708 3.709-9.678 3.709-21.617 3.709h-2.11c-11.94 0-17.91 0-21.619-3.71C0 45.348 0 39.378 0 27.439v-2.112Z"
                ></path><path
                  fill="#31D652"
                  d="m19.419 36.916.483.24c2.013 1.193 4.268 1.75 6.522 1.75 7.085 0 12.882-5.73 12.882-12.733 0-3.343-1.368-6.605-3.784-8.993a12.865 12.865 0 0 0-9.098-3.74c-7.085 0-12.882 5.73-12.802 12.812 0 2.388.725 4.696 1.932 6.685l.322.477-1.288 4.695 4.83-1.194Z"
                ></path><path
                  fill="#fff"
                  d="M36.891 15.905c-2.738-2.786-6.522-4.298-10.387-4.298-8.212 0-14.814 6.606-14.734 14.643 0 2.547.725 5.014 1.933 7.242l-2.094 7.56 7.81-1.989c2.174 1.194 4.59 1.75 7.005 1.75 8.132 0 14.734-6.605 14.734-14.642 0-3.9-1.53-7.56-4.267-10.266ZM26.504 38.347c-2.173 0-4.347-.557-6.2-1.67l-.482-.24-4.67 1.194 1.208-4.536-.322-.478c-3.543-5.65-1.852-13.13 3.945-16.633 5.797-3.5 13.285-1.83 16.827 3.9 3.543 5.73 1.852 13.13-3.945 16.632-1.852 1.194-4.106 1.83-6.36 1.83v.001Zm7.086-8.834-.886-.398s-1.288-.557-2.093-.955c-.08 0-.16-.08-.242-.08-.24 0-.402.08-.563.16 0 0-.08.08-1.208 1.353a.444.444 0 0 1-.403.24h-.08c-.08 0-.242-.08-.322-.16l-.403-.16c-.886-.398-1.69-.875-2.335-1.512-.16-.159-.402-.318-.563-.477-.564-.557-1.128-1.194-1.53-1.91l-.08-.16c-.08-.079-.08-.158-.162-.318 0-.159 0-.318.08-.398 0 0 .323-.398.564-.636.162-.16.242-.398.403-.557.161-.24.242-.557.161-.796-.08-.398-1.047-2.547-1.288-3.024-.16-.239-.322-.319-.564-.398h-.885c-.162 0-.322.08-.484.08l-.08.08c-.16.08-.322.238-.483.317-.16.16-.242.32-.403.478-.563.716-.885 1.591-.885 2.467 0 .637.16 1.273.402 1.83l.08.24a13.264 13.264 0 0 0 2.98 4.058l.322.318c.241.24.483.398.644.637 1.69 1.432 3.623 2.467 5.797 ************.564.08.805.16h.805c.403 0 .886-.16 1.208-.319.241-.16.402-.16.564-.319l.16-.159c.162-.16.323-.239.484-.398.16-.159.322-.318.402-.477.161-.319.242-.717.322-1.114v-.558s-.08-.08-.24-.159Z"
                ></path></svg
              >
            </a>
          </div>
          <h3 class="footer-cards__card-title">+56976335694</h3>
        </div>
      </div>
    </div>
  </section>
</Layout>

<script>
  // Swiper
  import Swiper from "swiper";
  import debounce from "@src/utils/debounce";

  //#region [Player Example Swiper]
  let playerExampleSwiper: Swiper | null = null;
  let swiperInitialized = false;

  function createSwiperOnMobile() {
    const isMobile = window.matchMedia("(max-width: 688px)");

    if (!swiperInitialized && isMobile.matches) {
      playerExampleSwiper = new Swiper(
        ".swiper.player-example__skills-slider",
        {
          spaceBetween: 16,
          slidesPerView: "auto",
          grabCursor: true,
        },
      );
      swiperInitialized = true;
    } else if (playerExampleSwiper && !isMobile.matches && swiperInitialized) {
      playerExampleSwiper.destroy();
      swiperInitialized = false;
    }
  }

  window.addEventListener("load", createSwiperOnMobile);
  window.addEventListener("resize", debounce(createSwiperOnMobile, 400));
  //#endregion [Player Example Swiper]

  //#region [Video]
  const myVideo = document.querySelector("video");
  if (myVideo) {
    const promise = myVideo.play();

    promise.then(
      (res) => {},
      (rej) => {
        const playButton = document.querySelector(
          ".video__play-button",
        ) as HTMLElement;
        if (playButton) {
          playButton.style.display = "block";
          myVideo.style.cursor = "pointer";
          myVideo.addEventListener("click", () => {
            if (myVideo.paused) {
              myVideo.play();
              myVideo.style.cursor = "";
              playButton.style.display = "";
            }
          });
        }
      },
    );
  }
  //#endregion [Video]
</script>

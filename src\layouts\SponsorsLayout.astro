---
//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

//#endregion [i18n]

//#region [Styles]
import "@styles/pages/sponsors.scss";
//#endregion [Styles]

//#region [Built-in components]
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
//#endregion [Components]

//#region [Icons]
//#endregion [Icons]

//#region [Images]
//#endregion [Images]

//#region [Videos]
import SponsorVideo from "@assets/videos/sponsors/sponsor.mp4";
//#endregion [Videos]
---

<Layout>
  <section id="hero">
    <div class="container">
      <div class="hero">
        <div class="hero__col">
          <video class="hero__video-tag" autoplay loop muted playsinline>
            <source src={SponsorVideo} />
          </video>
        </div>
        <div class="hero__col">
          <h1 class="hero__title">{t("sponsors.title")}</h1>
          <p class="hero__subtitle">
            {t("sponsors.text")}
          </p>
          <div class="hero__contacts">
            <p>{t("sponsors.contact")}</p>
            <p>
              WhatsApp
              <a
                class="hero__contacts-link highlight-link"
                href="https://wa.me/35794426111"
                target="_blank">+357 94426111</a
              >
            </p>
            <p>
              Email
              <a
                class="hero__contacts-link highlight-link"
                href="mailto:<EMAIL>"
                target="_blank"><EMAIL></a
              >
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout>

---
//#region [Built-in components]
import { Image, getImage } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
import { languagesMotril } from "src/lang/langs";
import AppButton from "@components/AppButton.astro";
import RocketLaunch from "@components/icons/RocketLaunch.astro";

//#endregion [Components]

//#region [Styles]

//#endregion [Styles]

//#region [Icons]
//#endregion [Icons]

//#region [Images]
import LogoImg from "@assets/images/academies/motril/motril-logo.svg";

import HugoImg from "@assets/images/academies/motril/player1.png";
import ElijahImg from "@assets/images/academies/motril/player2.png";

const TestIcons = await Astro.glob(
  "/src/assets/images/academies/motril/tests/*.{png,svg,jpg}",
).then((files: any) => {
  return files.map((file: any) => {
    return file.default;
  });
});

import VideoPosterImg from "@assets/images/talents/video-poster.jpg";
const VideoPosterImgOptimized = await getImage({
  src: VideoPosterImg,
  format: "webp",
});
//#endregion [Images]

//#region [Videos]
import LanguagePicker from "@components/LanguagePicker.astro";
import talentVideoMp4 from "@assets/videos/academies/motril/motril-trial.mp4";
import talentVideoWebm from "@assets/videos/academies/motril/motril-trial.webm";
//#endregion [Videos]

const inviteMentorLink =
  "https://app.junistat.com/invite-mentors?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2FkZW15SWQiOiIzMDE1YmMzYS1jNDAxLTQ2MWQtYTVjYy03YzRmZjEzOWU3MDMiLCJpYXQiOjE3NDU1OTg2OTQsImV4cCI6MTc1MzM3NDY5NH0.e8LiJ7sFRK7Dueb2yCCAbTlA-7-3aVoMjKTyCDPBO-A";
---

<Layout
  title="Scouting and selecting with JuniStat"
  contentOnly
>
  <header class="motril-header">
    <LanguagePicker
      class="header__lang-selector p2"
      langArray={languagesMotril}
    />
  </header>

  <section class="section is--hero">
    <div class="container">
      <div class="hero">
        <Image class="hero__logo" src={LogoImg} alt="logo" />
        <h1 class="hero__headline">
          Online talent assessment and recruitment program
        </h1>
        <h3>powered by JuniStat® — smart testing system</h3>
      </div>
    </div>
  </section>
  <section class="section section-space">
    <div class="container">
      <main class="main">
        <div class="main__col guide">
          <h2>
            Step-by-Step guide to creating player’s profile and completing tests
          </h2>
          <div class="card guide__card">
            <div class="guide__circle-number h2">1</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Registration</h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>Click the registration link and sign up</p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    Follow the instructions sent to your email to install the
                    JuniStat app and set up your web dashboard
                  </p>
                </li>
              </ul>
              <AppButton
                as="a"
                style="mod2"
                class="reg-button"
                skew
                href={inviteMentorLink}
                target="_blank"
              >
                <RocketLaunch />
                <span class="h3" style="font-weight: 700">Get Started</span>
              </AppButton>
            </div>
          </div>
          <div class="card guide__card">
            <div class="guide__circle-number h2">2</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Subscription payment</h3>
              <div class="guide__card-price-block">
                <h3 class="guide__card-price h4">€19</h3>
                <h3 class="guide__card-price-old h4">€99</h3>
              </div>

              <p>Annual licence payment</p>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>
                    Make the payment through the web dashboard to unlock tests
                    within the app by entering the promo code <p
                      style="font-weight: 700"
                    >
                      MOTRIL
                    </p>
                  </p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    Once completed, the profile is shared directly with
                    professional clubs
                  </p>
                </li>
              </ul>
            </div>
          </div>
          <div class="card guide__card">
            <div class="guide__circle-number h2">3</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Profile Enhancement</h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>
                    Enhance your profile by uploading videos and providing a
                    detailed bio
                  </p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    This information will help us make a more informed decision
                    regarding your trial
                  </p>
                </li>
              </ul>
              <div class="guide__profile-examples">
                <h3 class="guide__card-headline h4">
                  Players’ profiles eхamples
                </h3>
                <div class="guide__profile-cards">
                  <a
                    class="guide__card-link"
                    href="https://app.junistat.com/player/3ea80d93-85cb-48b2-8f52-3c03ba4eef88"
                    target="_blank"
                  >
                    <Image src={HugoImg} alt="Profile example" width={340} />
                  </a>
                  <a
                    class="guide__card-link"
                    href="https://app.junistat.com/player/6c864e97-d3c2-4a97-9abd-7c74bb7e78f5"
                    target="_blank"
                  >
                    <Image src={ElijahImg} alt="Profile example" width={340} />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="main__col info">
          <div class="card info">
            <h2>
              We are actively reviewing players <span
                class="motril-select-text"
              >
                U16+
              </span> who have registered and excelled in JuniStat tests
            </h2>

            <div class="info__video video">
              <video
                class="info__video-tag"
                poster={VideoPosterImgOptimized.src}
                style="width: 100%"
                controls
                playsinline
              >
                <source src={talentVideoWebm} type="video/webm" />
                <source src={talentVideoMp4} type="video/mp4" />
              </video>
            </div>
          </div>
          <div class="card info">
            <div class="info__card-header">
              <h2>
                Please complete the following 5 tests in the App and showcase
                your talent!
              </h2>
              <p>
                Once all the tests are completed, our recruitment team will
                review your profile and contact you with the feedback
              </p>
            </div>
            <div class="info__tests-wrapper">
              <h2 class="info__category-headline">Required tests</h2>
              <div class="info__tests-group">
                <div class="info__test">
                  <Image src={TestIcons[0]} alt="Test icon" />
                  <p>15m sprint with run up</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[5]} alt="Test icon" />
                  <p>15m sprint from the start</p>
                </div>

                <div class="info__test">
                  <Image src={TestIcons[6]} alt="Test icon" />
                  <p>High jump</p>
                </div>

                <div class="info__test">
                  <Image src={TestIcons[4]} alt="Test icon" />
                  <p>Arrow test</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[7]} alt="Test icon" />
                  <p>Serpent</p>
                </div>
              </div>

              <AppButton
                as="a"
                style="mod2"
                skew
                class="reg-button"
                href={inviteMentorLink}
                target="_blank"
              >
                <RocketLaunch />
                <span class="h3" style="font-weight: 700">Get Started</span>
              </AppButton>
            </div>
            <div class="card info">
              <div class="info__card-header">
                <p>
                  I am here to help with any inquiries you may have. Please feel
                  free to reach out <a
                    class="link_underline"
                    href="mailto:<EMAIL>"
                    target="_blank"><EMAIL></a
                  >
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </section>

  <style>
    @import "@styles/pages/academies/motril.scss";
  </style>

  <script>
    import { track } from "@amplitude/analytics-browser";

    const regButtons = document.querySelectorAll(
      ".reg-button",
    ) as NodeListOf<HTMLElement>;

    regButtons.forEach((button) => {
      button.addEventListener("click", () => {
        track("Click registration button", {
          academy: "MOTRIL",
        });
      });
    });
  </script>
</Layout>

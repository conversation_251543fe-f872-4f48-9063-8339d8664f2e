---
//#region [Built-in components]
import { Image, getImage } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
import AppButton from "@components/AppButton.astro";
import RocketLaunch from "@components/icons/RocketLaunch.astro";

//#endregion [Components]

//#region [Styles]

//#endregion [Styles]

//#region [Icons]
//#endregion [Icons]

//#region [Images]
import LogoImg from "@assets/images/academies/whuf/whuf-logo.svg";

import HugoImg from "@assets/images/academies/whuf/hugo-scaled.png";
import ElijahImg from "@assets/images/academies/whuf/elijah-scaled.png";

const TestIcons = await Astro.glob(
  "/src/assets/images/academies/whuf/tests/*.{png,svg,jpg}"
).then((files: any) => {
  return files.map((file: any) => {
    return file.default;
  });
});

import VideoPosterImg from "@assets/images/talents/video-poster.jpg";
const VideoPosterImgOptimized = await getImage({
  src: VideoPosterImg,
  format: "webp",
});
//#endregion [Images]

//#region [Videos]
import talentVideoMp4 from "@assets/videos/academies/whuf/whuf-trial.mp4";
import talentVideoWebm from "@assets/videos/academies/whuf/whuf-trial.webm";
//#endregion [Videos]

const inviteMentorLink = "https://junistat.onelink.me/NPUG/whu";
---

<Layout title="Scouting and selecting with JuniStat" contentOnly>
  <section class="section is--hero">
    <div class="container">
      <div class="hero">
        <Image
          class="hero__logo"
          src={LogoImg}
          alt="West Ham United Foundation - The Academy of Football"
        />
        <h1 class="hero__headline">
          Online talent assessment and recruitment program
        </h1>
        <h2>powered by JuniStat<sup>®</sup> — smart testing system</h2>
      </div>
    </div>
  </section>
  <section class="section section-space">
    <div class="container">
      <main class="main">
        <div class="main__col guide">
          <h2>
            Step-by-Step guide to creating player’s profile and completing tests
          </h2>
          <div class="card guide__card">
            <div class="guide__circle-number h2">1</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Registration</h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>Click the registration link and sign up</p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    Follow the instructions sent to your email to install the
                    JuniStat app and set up your web dashboard
                  </p>
                </li>
              </ul>
              <AppButton
                as="a"
                style="mod2"
                class="reg-button"
                skew
                href={inviteMentorLink}
                target="_blank"
              >
                <RocketLaunch />
                <span class="h3" style="font-weight: 700">Get Started</span>
              </AppButton>
            </div>
          </div>
          <div class="card guide__card">
            <div class="guide__circle-number h2">2</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Subscription payment</h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>
                    Make the subscription payment through the web dashboard
                    to access tests within the app
                  </p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    Once completed, scouts can monitor the player’s profile and
                    test results.
                  </p>
                </li>
              </ul>
            </div>
          </div>
          <div class="card guide__card">
            <div class="guide__circle-number h2">3</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Profile Enhancement</h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>
                    Enhance your profile by uploading videos and providing a
                    detailed bio.
                  </p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    This information will help us make a more informed decision
                    regarding your trial
                  </p>
                </li>
              </ul>
              <div class="guide__profile-examples">
                <h3 class="guide__card-headline h4">
                  Players’ profiles eхamples
                </h3>
                <div class="guide__profile-cards">
                  <a
                    class="guide__card-link"
                    href="https://app.junistat.com/player/3ea80d93-85cb-48b2-8f52-3c03ba4eef88"
                    target="_blank"
                  >
                    <Image src={HugoImg} alt="Profile example" width={340} />
                  </a>
                  <a
                    class="guide__card-link"
                    href="https://app.junistat.com/player/6c864e97-d3c2-4a97-9abd-7c74bb7e78f5"
                    target="_blank"
                  >
                    <Image src={ElijahImg} alt="Profile example" width={340} />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="main__col info">
          <div class="card info">
            <h2>
              We are actively reviewing players who have registered and excelled
              in JuniStat tests
            </h2>

            <div class="info__video video">
              <video
                class="info__video-tag"
                poster={VideoPosterImgOptimized.src}
                style="width: 100%"
                controls
                playsinline
              >
                <source src={talentVideoWebm} type="video/webm" />
                <source src={talentVideoMp4} type="video/mp4" />
              </video>
            </div>
          </div>
          <div class="card info">
            <div class="info__card-header">
              <h2>
                Please complete the following 8 tests in the App and showcase
                your talent!
              </h2>
              <p>
                Once all the tests are completed, our recruitment team will
                review your profile and contact you with their feedback
              </p>
            </div>
            <div class="info__tests-wrapper">
              <h2 class="info__category-headline">Required tests</h2>
              <div class="info__tests-group">
                <div class="info__test">
                  <Image src={TestIcons[6]} alt="Test icon" />
                  <p>High jump</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[1]} alt="Test icon" />
                  <p>Push ups</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[0]} alt="Test icon" />
                  <p>15m sprint from run up</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[7]} alt="Test icon" />
                  <p>Serpent</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[5]} alt="Test icon" />
                  <p>15m sprint</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[4]} alt="Test icon" />
                  <p>Arrow</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[3]} alt="Test icon" />
                  <p>Arrow with the ball. RF</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[2]} alt="Test icon" />
                  <p>Arrow with the ball. LF</p>
                </div>
              </div>
            </div>

            <AppButton
              as="a"
              style="mod2"
              skew
              class="reg-button"
              href={inviteMentorLink}
              target="_blank"
            >
              <RocketLaunch />
              <span class="h3" style="font-weight: 700">Get Started</span>
            </AppButton>
          </div>
          <div class="card info">
            <div class="info__card-header">
              <p>
                We are here to help with any inquiries you may have. Please feel
                free to reach out to us <a
                  href="mailto:<EMAIL>"
                  target="_blank"><EMAIL></a
                >
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  </section>
</Layout>

<style>
  @import "@styles/pages/academies/whuf.scss";
</style>

<script>
  import { track } from "@amplitude/analytics-browser";

  const regButtons = document.querySelectorAll(
    ".reg-button"
  ) as NodeListOf<HTMLElement>;

  regButtons.forEach((button) => {
    button.addEventListener("click", () => {
      track("Click registration button", {
        academy: "WHUF",
      });
    });
  });
</script>

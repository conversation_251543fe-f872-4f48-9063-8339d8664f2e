---
//#region [Built-in components]
import { Image, getImage } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
import AppButton from "@components/AppButton.astro";
import RocketLaunch from "@components/icons/RocketLaunch.astro";

//#endregion [Components]

//#region [Styles]

//#endregion [Styles]

//#region [Icons]
//#endregion [Icons]

//#region [Images]
import OmamoImg from "@assets/images/talents/omamo.png";
import CordnerImg from "@assets/images/talents/cordner.png";
import MirkoImg from "@assets/images/talents/mirko.png";

const TestIcons = await Astro.glob(
  "/src/assets/images/talents/tests/*.{png,svg,jpg}",
).then((files) => {
  return files.map((file) => {
    return file.default;
  });
});

import VideoPosterImg from "@assets/images/talents/video-poster.jpg";
const VideoPosterImgOptimized = await getImage({
  src: VideoPosterImg,
  format: "webp",
});
//#endregion [Images]

//#region [Videos]
import talentVideoMp4 from "@assets/videos/talents/talent-web.mp4";
import talentVideoWebm from "@assets/videos/talents/talent-web.webm";
//#endregion [Videos]
---

<Layout title="Scouting and selecting with JuniStat" contentOnly noindex>
  <section class="section">
    <div class="container">
      <div class="hero">
        <h1 class="hero__headline">
          Scouting and selecting young footballers for the trials in the EU and
          UK academies through online testing.
        </h1>
        <p class="hero__text h3">
          JuniStat<sup>®</sup> - Smart testing system proved by club academies
          and federations for the comprehensive player assessment.
        </p>
      </div>
    </div>
  </section>
  <section class="section section-space">
    <div class="container">
      <main class="main">
        <div class="main__col guide">
          <h2>
            Step-by-Step guide to creating player’s profile and completing tests
          </h2>
          <div class="card guide__card">
            <div class="guide__circle-number h2">1</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Registration</h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>Click the registration link and sign up</p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    Follow the instructions sent to your email to install the
                    JuniStat app and set up your web dashboard
                  </p>
                </li>
              </ul>
              <AppButton
                as="a"
                style="mod2"
                class="reg-button"
                skew
                href="#"
                target="_blank"
              >
                <RocketLaunch />
                <span class="h3" style="font-weight: 700">Register</span>
              </AppButton>
            </div>
          </div>
          <div class="card guide__card">
            <div class="guide__circle-number h2">2</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Subscription payment</h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>
                    Make the subscription payment through the web dashboard
                    to access tests within the app
                  </p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    Once completed, scouts can monitor the player’s profile and
                    test results.
                  </p>
                </li>
              </ul>
            </div>
          </div>
          <div class="card guide__card">
            <div class="guide__circle-number h2">3</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Profile Enhancement</h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>
                    Enhance your profile by uploading videos and providing a
                    detailed bio.
                  </p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    This information will help us make a more informed decision
                    regarding your trial
                  </p>
                </li>
              </ul>
              <div class="guide__profile-examples">
                <h3 class="guide__card-headline h4">
                  Players’ profiles eхamples
                </h3>
                <div class="guide__profile-cards">
                  <a
                    class="guide__card-link"
                    href="https://app.junistat.com/player/2b88f663-a67f-45ec-b801-2afa3b9956e3"
                    target="_blank"
                  >
                    <Image src={OmamoImg} alt="Profile example" width={254} />
                  </a>
                  <a
                    class="guide__card-link"
                    href="https://app.junistat.com/player/6c864e97-d3c2-4a97-9abd-7c74bb7e78f5"
                    target="_blank"
                  >
                    <Image src={CordnerImg} alt="Profile example" width={254} />
                  </a>
                  <a
                    class="guide__card-link"
                    href="https://app.junistat.com/player/6d9d7e08-40ae-4b95-b943-1628ee4d8d58"
                    target="_blank"
                  >
                    <Image src={MirkoImg} alt="Profile example" width={254} />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="main__col info">
          <div class="card info">
            <h2>
              We are actively reviewing players who have registered and excelled
              in JuniStat tests
            </h2>

            <div class="info__video video">
              <video
                class="info__video-tag"
                poster={VideoPosterImgOptimized.src}
                style="width: 100%"
                controls
                playsinline
              >
                <source src={talentVideoWebm} type="video/webm" />
                <source src={talentVideoMp4} type="video/mp4" />
              </video>
            </div>
          </div>
          <div class="card info">
            <div class="info__card-header">
              <h2>
                Please complete the following 3 tests in the App and showcase
                your talent!
              </h2>
              <p>
                Once all the tests are completed, our recruitment team will
                review your profile and contact you with their feedback
              </p>
            </div>
            <div class="info__tests-wrapper">
              <h2 class="info__category-headline">Required tests</h2>
              <div class="info__tests-group">
                <div class="info__test">
                  <Image src={TestIcons[2]} alt="Test icon" />
                  <p>High jump</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[1]} alt="Test icon" />
                  <p>15m sprint</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[4]} alt="Test icon" />
                  <p>Serpent</p>
                </div>
              </div>
            </div>
            <div class="info__tests-wrapper">
              <h2 class="info__category-headline">Optional</h2>
              <div class="info__tests-group">
                <div class="info__test">
                  <Image src={TestIcons[5]} alt="Test icon" />
                  <p>Power kick</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[0]} alt="Test icon" />
                  <p>Arrow</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[3]} alt="Test icon" />
                  <p>Other tests</p>
                </div>
              </div>
            </div>
            <AppButton
              as="a"
              style="mod2"
              skew
              class="reg-button"
              href="#"
              target="_blank"
            >
              <RocketLaunch />
              <span class="h3" style="font-weight: 700">Register</span>
            </AppButton>
          </div>
          <div class="card info">
            <div class="info__card-header">
              <p>
                We are here to help with any inquiries you may have. Please feel
                free to reach out to us <a
                  href="mailto:<EMAIL>"
                  target="_blank"><EMAIL></a
                >
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  </section>
</Layout>

<style>
  @import "@styles/pages/talent.scss";
</style>

<script>
  import { track } from "@amplitude/analytics-browser";

  const regButtons = document.querySelectorAll(
    ".reg-button",
  ) as NodeListOf<HTMLElement>;

  regButtons.forEach((button) => {
    button.addEventListener("click", () => {
      track("Click registration button");
    });
  });
</script>

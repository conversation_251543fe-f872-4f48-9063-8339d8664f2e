/*#region [Font]*/
@font-face {
  font-family: "Onest";
  src: url("../../assets/fonts/Onest-Regular.woff2") format("woff2");
  font-weight: 400;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Onest";
  src: url("../../assets/fonts/Onest-Medium.woff2") format("woff2");
  font-weight: 500;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Onest";
  src: url("../../assets/fonts/Onest-Bold.woff2") format("woff2");
  font-weight: 700;
  font-display: swap;
  font-style: normal;
}

html {
  font-size: 1vw;
}

@media screen and (min-width: 1440px) {
  html {
    font-size: 16px;
  }
}

body {
  font-family: "Onest", Inter, Arial, Helvetica, sans-serif;
}

h1,
.h1 {
  font-weight: 700;
  font-size: 5.3rem;
}
@media screen and (max-width: 688px) {
  h1,
  .h1 {
    font-size: 11rem;
  }
}

h2,
.h2 {
  font-weight: 700;
  font-size: 3.47rem;
}
@media screen and (max-width: 688px) {
  h2,
  .h2 {
    font-size: 29px;
  }
}

h3,
.h3 {
  font-weight: 500;
  font-size: 2.22rem;
}
@media screen and (max-width: 992px) {
  h3,
  .h3 {
    font-size: 24px;
  }
}

h4,
.h4 {
  font-size: 24px;
}
@media screen and (max-width: 688px) {
  h4,
  .h4 {
    font-size: 21px;
  }
}

p,
.p {
  font-size: 16px;
}
/*#endregion [Font]*/

.container {
  max-width: 88.89rem;
}
@media screen and (max-width: 688px) {
  .container {
    max-width: 100%;
    padding-left: 16px;
    padding-right: 16px;
  }
}

section {
  overflow: unset;
}

.overflow-hidden {
  overflow: hidden;
}

@media screen and (max-width: 688px) {
  .hide-on-mobile {
    display: none;
  }
}

@media screen and (min-width: 688px) {
  .hide-on-desktop {
    display: none;
  }
}

@media screen and (max-width: 688px) {
  .section-space {
    margin-top: 19.2rem;
  }
}

/*#region [Header]*/
header {
  margin-top: 64px;
}

.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

@media screen and (max-width: 688px) {
  .header {
    align-items: flex-start;
    flex-direction: column;
  }
}

.header__logo-wrap {
  max-width: 226px;
}
/*#endregion [Header]*/

/*#region [Hero]*/
#hero {
  margin-top: 32px;
}

.hero__content {
  display: flex;
  flex-direction: row;
  gap: 32px;
  margin-top: 48px;
}
@media screen and (max-width: 992px) {
  .hero__content {
    display: flex;
    flex-direction: column;
  }
}

.hero__headline {
  max-width: 860px;
}

.hero__image {
  max-width: none;
  height: auto;
}
@media screen and (max-width: 688px) {
  .hero__image {
    width: 560px;
  }
}

.hero__rfs {
  display: flex;
  flex-direction: column;
  flex: 0 0 40%;
  padding: 24px 32px;
  color: var(--white);
  background-color: #2ed887;
  border-radius: 12px;
}
@media screen and (max-width: 992px) and (min-width: 688px) {
  .hero__rfs {
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 24px;
  }
}

.hero__rfs-text {
  margin-top: auto;
}

@media screen and (max-width: 688px) {
  .hero__rfs-text {
    font-size: 18px;
  }
}
@media screen and (max-width: 992px) and (min-width: 688px) {
  .hero__rfs-text {
    margin-top: 0;
  }
}

@media screen and (max-width: 688px) {
  .hero__rfs-text {
    margin-top: 16px;
  }
}

.hero__rfs-letter-link {
  margin-top: 8px;
}

@media screen and (min-width: 992px) {
  .hero__rfs-letter-link {
    transition: opacity 0.3s var(--smooth-ease);
  }
  .hero__rfs-letter-link:hover {
    opacity: 0.7;
  }
}
/*#endregion [Hero]*/

/*#region [Tool]*/
.tool__headline-wrap {
  max-width: 1280px;
}

.tool__points {
  display: flex;
  flex-direction: row;
  margin-top: 40px;
  gap: 32px;
  column-gap: 56px;
}
@media screen and (max-width: 688px) {
  .tool__points {
    flex-direction: column;
  }
}

.tool__point {
  max-width: 460px;
}

.tool__register-button {
  margin-top: 40px;
}
/*#endregion [Tool]*/

/*#region [Benefits]*/
.benefits__content {
  display: flex;
  gap: 32px;
  margin-top: 48px;
}
@media screen and (max-width: 992px) {
  .benefits__content {
    display: block;
  }
}

.benefits__cards {
  display: flex;
  flex-direction: column;
  gap: 24px;
  flex: 0 0 50%;
}

.benefits__card {
  display: flex;
  padding: 24px 144px 24px 24px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  align-self: stretch;
  position: relative;
  border-radius: 12px;
  background: #fff;
  overflow: hidden;
}
@media screen and (max-width: 992px) {
  .benefits__card {
    padding: 16px;
    gap: 24px;
  }
}

.benefits__card-icon {
  position: absolute;
  top: 10%;
  right: 0;
  transform: rotateZ(15deg);
}
@media screen and (max-width: 992px) {
  .benefits__card-icon {
    position: static;
    transform: none;
  }
}

.benefits__button {
  width: 100%;
}

@media screen and (max-width: 992px) {
  .benefits__content > picture {
    display: none;
  }

  picture + .benefits__card-icon {
    display: none;
  }
}

@media screen and (min-width: 992px) {
  .benefits__players-cards.is--mobile {
    display: none;
  }
}

/*#endregion [Benefits]*/

/*#region [Tests]*/
.container.is--tests {
  position: relative;
  overflow: visible;
}

.tests__head {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
@media screen and (max-width: 992px) {
  .tests__head {
    flex-direction: column;
    gap: 24px;
  }
}

@media screen and (min-width: 992px) {
  .tests__head-title-wrap {
    max-width: 60%;
  }
}

.tests.swiper-wrapper {
  margin-top: 48px;
}

.tests.swiper-wrapper {
  gap: 16px;
}

@media screen and (max-width: 992px) {
  .tests.swiper-wrapper {
    gap: 0px;
  }
}

.tests__slide-group {
  display: flex;
  gap: 16px;
  margin-left: 16px;
}

/* .tests__slide-group > .swiper-slide + .swiper-slide {
  margin-left: 16px;
} */

.test {
  width: 100%;
}

.tests__slide-group .test.is--small {
  width: 33%;
}

.tests__slide-group .test.is--medium {
  width: 49%;
}

.tests__headline {
  margin-top: 8px;
  margin-bottom: 16px;
}

/*#region [Pagination]*/
.tests__navigation-wrap {
  align-items: flex-end;
  margin-top: auto;
  min-height: 48px;
  /* display: none; */
}

@media screen and (max-width: 992px) {
  .tests__navigation-wrap {
    display: flex;
  }
}

.tests__navigation {
  width: 100%;
  display: flex;
  gap: 24px;
  justify-content: center;
  align-items: center;
  background: rgba(217, 217, 217, 0.7);
  border: 1px solid #f1f1f1;
  border-radius: 8px;
  padding: 0.25rem;
}

.tests__navigation-button {
  background: rgba(255, 255, 255, 0.8);
  width: 100%;
}

@media screen and (min-width: 992px) {
  .tests__navigation-button:hover {
    background: rgba(255, 255, 255, 1);
  }
}

.tests__navigation-counter {
  white-space: nowrap;
  text-align: center;
}

.tests__pagination {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.tests__navigation-wrap.is--mobile {
  display: none;
}

@media screen and (max-width: 992px) {
  .tests__navigation-wrap {
    display: none;
  }

  .tests__navigation-wrap.is--mobile {
    margin-top: 24px;
    display: flex;
  }
}

.swiper-pagination-bullet {
  width: 16px;
  height: 16px;
  margin: 8 !important;
  border-radius: 2px;
}

.swiper-pagination-bullet-active {
  background-color: var(--primary);
}
/*#endregion [Pagination]*/

/*#endregion [Tests]*/

/*#region [Price]*/
#price {
  margin-top: 88px;
}

.price__grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 16px;
  margin-top: 48px;
}
@media screen and (max-width: 992px) {
  .price__grid {
    display: flex;
    flex-direction: column;
  }
}

.price__plan {
  padding: 24px;
  border-radius: 12px;
  background-color: var(--white);
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.price__plan.is--big {
  grid-row: 2 span;
}

.price__plan.is--special {
  grid-column: 2 span;
}

.price__plan-price-wrap {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.price__plan-price-sum {
  font-weight: 700;
}

hr {
  width: 100%;
  border: 0;
  border-top: 1px solid #e5e5e5;
}

.price__plan-points {
  padding-inline-start: 32px;
}

.price__plan-point + .price__plan-point {
  margin-top: 16px;
}

li.price__plan-point {
  line-height: 120%;
  position: relative;
}

li.price__plan-point::before {
  content: "";
  display: block;
  width: 24px;
  height: 24px;
  position: absolute;
  top: 0;
  left: -32px;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgZmlsbD0ibm9uZSIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSIjMkVEODg3IiBkPSJNMTIgMjJDNi40NzcgMjIgMiAxNy41MjMgMiAxMlM2LjQ3NyAyIDEyIDJzMTAgNC40NzcgMTAgMTAtNC40NzcgMTAtMTAgMTBabS0uOTk3LTYgNy4wNy03LjA3MS0xLjQxMy0xLjQxNC01LjY1NyA1LjY1Ny0yLjgyOS0yLjgyOS0xLjQxNCAxLjQxNEwxMS4wMDMgMTZaIi8+PC9zdmc+");
}

.price__link {
  color: var(--blue);
  transition: color 0.2s var(--smooth-ease);
}

@media screen and (any-hover: hover) {
  .price__link:hover {
    color: var(--accent);
  }
}

.price__register-button {
  margin-top: 32px;
}
/*#endregion [Price]*/

/*#region [Help]*/
.help__card[href] {
  transition:
    box-shadow 0.2s var(--smooth-ease),
    transform 0.2s var(--smooth-ease);
}

@media screen and (any-hover: hover) {
  .help__card[href]:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transform: translateY(-4px) !important;
  }
}

/*#endregion [Help]*/

footer {
  margin-top: 64px;
  margin-bottom: 64px;
  text-align: center;
}

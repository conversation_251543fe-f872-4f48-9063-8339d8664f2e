/*#region [Font]*/
@font-face {
  font-family: "Onest";
  src: url("../../assets/fonts/Onest-Regular.woff2") format("woff2");
  font-weight: 400;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Onest";
  src: url("../../assets/fonts/Onest-Medium.woff2") format("woff2");
  font-weight: 500;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Onest";
  src: url("../../assets/fonts/Onest-Bold.woff2") format("woff2");
  font-weight: 700;
  font-display: swap;
  font-style: normal;
}

@media screen and (min-width: 1440px) {
  html {
    font-size: 16px;
  }
}

body {
  font-family: "Onest", Inter, Arial, Helvetica, sans-serif;
  font-size: clamp(18px, 0.793rem + 0.88vw, 24px);
  line-height: 120%;
}

h1,
.h1 {
  font-weight: 700;
  font-size: clamp(38px, 1.378rem + 2.65vw, 56px);
}

h2,
.h2 {
  font-weight: 700;
  font-size: clamp(32px, 1.086rem + 1.77vw, 40px);
}
/*#endregion [Font]*/

/*#region [Rating]*/
.rating {
  position: relative;
}

/*#region [Example]*/
#example {
  margin-top: 48px;
}

.example {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 64px;
}
@media screen and (max-width: 992px) {
  .example {
    flex-direction: column;
  }
}

.example__card-img,
.example__info {
  max-width: 100%;
}

.example__card {
  align-self: center;
}

.example__card-img {
  max-width: 780px;
}

@media screen and (max-width: 688px) {
  .example__card-img {
    min-width: 480px;
  }

  .example__card {
    align-self: flex-start;
  }
}

.example__info-list {
  margin-top: 32px;
}

.example__info-list * + * {
  margin-top: 24px;
}

@media screen and (max-width: 992px) {
  .example__info-list * + * {
    margin-top: 16px;
  }
}

ol,
ul {
  list-style: none;
  padding: 0;
}

li {
  position: relative;
  padding-left: 24px;
}

li::before {
  content: "";
  position: absolute;
  left: 0px;
  top: 0.13em;
  width: 8px;
  height: 16px;
  background-color: var(--gray);
  border-radius: 4px;
}

.example__download {
  display: flex;
  gap: 24px;
  margin-top: 32px;
}

.example__qr-code {
  border-radius: 8px;
  max-width: 128px;
}

@media screen and (max-width: 688px) {
  .download-buttons {
    margin-top: 0;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    align-self: center;
  }

  .download-buttons > * {
    height: 52px;
  }
}

/*#endregion [Example]*/
/*#region [Header]*/
.rating__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 48px;
}

@media screen and (max-width: 688px) {
  .rating__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

@media screen and (max-width: 688px) {
  .rating__header-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    width: 100%;
  }
}

.search__input-wrap {
  position: relative;
}

.overflow-visible {
  overflow: visible;
}

/*#endregion [Header]*/
/*#endregion [Rating]*/

/*#region [Scout]*/
.scout {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
  padding: 56px 32px;

  border-radius: 8px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #fff 100%);
}

@media screen and (max-width: 688px) {
  .scout {
    padding: 32px 16px;
  }
}

.scout__info {
  max-width: 820px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

form#scout-form {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.scount__inputs-wrap {
  display: flex;
  align-items: stretch;
  justify-content: center;
  gap: 16px;
}

@media screen and (max-width: 688px) {
  .scount__inputs-wrap {
    flex-direction: column;
  }
}

.scout__form-input {
  width: 100%;
  max-width: 368px;
  padding: 16px 8px 16px 16px;
  border: 1px solid var(--gray);
  background: #fff;
  border-radius: 8px;
}

#scout-btn {
  padding: 16px 32px;
}

#scout-btn > * {
  font-size: 21px;
  font-weight: 500;
}

.scout__form-terms {
  margin-top: 16px;
  font-size: 16px;
  text-align: center;
}

.scout__form-terms a {
  color: var(--blue);
}

.scout__form-result {
  display: none;
  max-width: 480px;
  border-radius: 8px;
  padding: 16px;
  background: #fff;
  font-size: 18px;
}

.scout__form-result.is--success {
  border: 2px solid #81e5a0;
}

.scout__form-result.is--error {
  border: 2px solid #e5a081;
  background: #f9f2f2;
}
/*#endregion [Scout]*/

/*#region [CTA]*/
.cta {
  padding: 64px 128px;
  color: white;
  background-position: 0% 0%;
  background-repeat: no-repeat;
  background-size: cover;
  overflow: hidden;
  border-radius: 16px;
}

@media screen and (max-width: 688px) {
  .cta {
    background-position: 40% 0%;
    padding: 32px 16px;
  }
}

.cta__subtitle {
  margin-top: 16px;
  margin-bottom: 48px;
}

.download-button:hover {
  opacity: 60%;
}

/*#endregion [CTA]*/
/*#region [Footer cards]*/
.footer-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

@media screen and (max-width: 688px) {
  .footer-cards {
    display: flex;
    flex-direction: column;
  }
}

.footer-cards__card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 24px;
  text-align: center;
  gap: 24px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid var(--gray);
}

.footer-cards__card:hover {
  background-color: #f7f7f7;
}

.footer-cards__card.is--material {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  text-align: left;
}

@media screen and (max-width: 688px) {
  .footer-cards__card.is--material {
    flex-direction: column;
    align-items: flex-start;
  }
}

.footer-cards__card.is--accent {
  background-color: var(--primary);
}

.footer-cards__card-side {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

@media screen and (max-width: 688px) {
  .footer-cards__card-side {
    width: 100%;
  }
}

.footer-cards__card-text {
  width: 80%;
}

@media screen and (max-width: 688px) {
  .footer-cards__card-text {
    width: 100%;
  }
}

.footer-cards__card-icon {
  width: 64px;
  height: 64px;
}

.download-buttons.is--footer {
  justify-content: center;
  align-items: center;
  margin-top: 0;
}

/*#endregion [Footer cards]*/
/*#region [SignUp modal]*/
.signup-modal {
  display: flex;
  flex-direction: column;
  row-gap: 24px;
  position: relative;
  align-items: center;
  justify-content: flex-start;
  text-align: center;
}

.signup-modal__close-icon {
  width: 32px;
  height: 32px;
  cursor: pointer;
  position: absolute;
  right: 16px;
  top: 16px;
}

.signup-modal__close-icon:hover {
  opacity: 0.4;
}

.signup-modal__icon {
  max-width: 128px;
}

@media screen and (min-width: 992px) {
  .signup-modal__title {
    font-size: 2rem;
  }
}

.signup-modal__description {
  margin-top: 16px;
}

@media screen and (min-width: 992px) {
  .signup-modal__description {
    font-size: 1.5rem;
  }
}

.signup-modal__buttons {
  width: 100%;
}

.signup-modal__buttons > * + * {
  margin-top: 16px;
}

/*#endregion [SignUp modal]*/
